/***************************************************
 * Created by nany<PERSON>ingfeng on 2019/9/17 17:56. *
 ***************************************************/

it('should support patch.imports', () => {
  const config = require('../configs')()
  config.patch.imports([
    {
      libraryName: 'antd',
      libraryDirectory: 'lib',
      style: true
    },
    {
      libraryName: '@material-ui/core',
      libraryDirectory: '',
      camel2DashComponentName: false
    }
  ])

  const oo = config.toString()
  expect(oo).toMatchSnapshot()
})
