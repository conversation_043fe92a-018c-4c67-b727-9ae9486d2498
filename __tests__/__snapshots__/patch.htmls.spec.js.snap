// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`should support patch.htmls.1 1`] = `
Object {
  "browserslist": Array [
    "last 2 versions",
    "Firefox >= 33",
    "> 1%",
    "IE >= 11",
    "iOS >= 8",
    "Android >= 4",
    "chrome >= 39",
    "Edge >= 12",
    "Safari >= 9",
  ],
  "compilertarget": "es5",
  "htmls": Object {
    "*": Object {
      "a": "1111.hbs",
      "i": "0000.icon",
    },
  },
}
`;

exports[`should support patch.htmls.1 2`] = `
Object {
  "context": "/Users/<USER>/Repositories/torpedo/whispered-build",
  "devServer": Object {
    "clientLogLevel": "none",
    "compress": true,
    "contentBase": "/Users/<USER>/Repositories/torpedo/whispered-build",
    "disableHostCheck": true,
    "headers": Object {
      "Access-Control-Allow-Origin": "*",
    },
    "historyApiFallback": Object {
      "disableDotRule": true,
    },
    "hot": true,
    "hotOnly": true,
    "overlay": false,
    "progress": true,
    "quiet": false,
    "watchContentBase": false,
  },
  "devtool": "source-map",
  "mode": "test",
  "module": Object {
    "noParse": Array [
      /moment\\$/,
    ],
    "rules": Array [
      Object {
        "parser": Object {
          "requireEnsure": false,
          "system": false,
        },
      },
      Object {
        "test": /\\\\\\.svgx\\$/,
        "use": Array [
          Object {
            "loader": "@svgr/webpack",
          },
        ],
      },
      Object {
        "test": /\\\\\\.json5\\$/,
        "use": Array [
          Object {
            "loader": "json5-loader",
          },
        ],
      },
      Object {
        "test": /\\\\\\.\\(woff\\|woff2\\)\\?\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "application/font-woff",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.ttf\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "application/octet-stream",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.eot\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "application/vnd.ms-fontobject",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.svg\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "url-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "image/svg+xml",
              "name": "static/media/[name].[hash].[ext]",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.\\(bmp\\|png\\|jpe\\?g\\|gif\\)\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/i,
        "use": Array [
          Object {
            "loader": "url-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "name": "static/media/[name].[hash].[ext]",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.html\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "name": "static/html/[name].[hash].[ext]",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.hbs\\?\\$/,
        "use": Array [
          Object {
            "loader": "mustache-loader",
            "options": Object {
              "render": Object {
                "process_env_ASSET_PATH": "./",
              },
            },
          },
        ],
      },
      Object {
        "oneOf": Array [
          Object {
            "resource": /module\\\\\\.\\\\w\\+ss\\$/,
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": Object {
                    "localIdentName": "[local]--[hash:base64:8]",
                  },
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": false,
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.css\\$/,
      },
      Object {
        "oneOf": Array [
          Object {
            "resource": /module\\\\\\.\\\\w\\+ss\\$/,
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": Object {
                    "localIdentName": "[local]--[hash:base64:8]",
                  },
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "less-loader",
                "options": Object {
                  "javascriptEnabled": true,
                  "modifyVars": Object {},
                  "sourceMap": true,
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": false,
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "less-loader",
                "options": Object {
                  "javascriptEnabled": true,
                  "modifyVars": Object {},
                  "sourceMap": true,
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.less\\$/,
      },
      Object {
        "exclude": Array [
          /node_modules/,
          /assets\\(-dev\\)\\?/,
        ],
        "oneOf": Array [
          Object {
            "resource": /worker\\\\\\.\\[tj\\]sx\\?\\$/,
            "use": Array [
              Object {
                "loader": "workerize-loader",
              },
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.jsx\\?\\$/,
      },
      Object {
        "exclude": Array [
          /node_modules/,
          /assets\\(-dev\\)\\?/,
        ],
        "oneOf": Array [
          Object {
            "resource": /worker\\\\\\.\\[tj\\]sx\\?\\$/,
            "use": Array [
              Object {
                "loader": "workerize-loader",
              },
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
              Object {
                "loader": "ts-loader",
                "options": Object {
                  "compilerOptions": Object {
                    "allowSyntheticDefaultImports": true,
                    "declaration": true,
                    "emitDecoratorMetadata": true,
                    "esModuleInterop": true,
                    "experimentalDecorators": true,
                    "importHelpers": true,
                    "inlineSourceMap": false,
                    "jsx": "react",
                    "lib": Array [
                      "dom",
                      "es5",
                      "es6",
                      "es7",
                      "es2015.promise",
                      "es2018.promise",
                      "es2015.collection",
                      "es2015.core",
                      "es2015",
                      "es2016",
                      "es2016.array.include",
                      "es2017",
                      "es2017.object",
                      "es2018",
                      "es2015.iterable",
                    ],
                    "module": "ESNEXT",
                    "moduleResolution": "node",
                    "noEmitOnError": true,
                    "noFallthroughCasesInSwitch": true,
                    "noImplicitAny": true,
                    "noImplicitReturns": true,
                    "outDir": "dist/lib",
                    "removeComments": true,
                    "rootDir": "src",
                    "skipLibCheck": true,
                    "sourceMap": false,
                    "strictNullChecks": false,
                    "target": "es5",
                  },
                  "experimentalWatchApi": true,
                  "transpileOnly": true,
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
              Object {
                "loader": "ts-loader",
                "options": Object {
                  "compilerOptions": Object {
                    "allowSyntheticDefaultImports": true,
                    "declaration": true,
                    "emitDecoratorMetadata": true,
                    "esModuleInterop": true,
                    "experimentalDecorators": true,
                    "importHelpers": true,
                    "inlineSourceMap": false,
                    "jsx": "react",
                    "lib": Array [
                      "dom",
                      "es5",
                      "es6",
                      "es7",
                      "es2015.promise",
                      "es2018.promise",
                      "es2015.collection",
                      "es2015.core",
                      "es2015",
                      "es2016",
                      "es2016.array.include",
                      "es2017",
                      "es2017.object",
                      "es2018",
                      "es2015.iterable",
                    ],
                    "module": "ESNEXT",
                    "moduleResolution": "node",
                    "noEmitOnError": true,
                    "noFallthroughCasesInSwitch": true,
                    "noImplicitAny": true,
                    "noImplicitReturns": true,
                    "outDir": "dist/lib",
                    "removeComments": true,
                    "rootDir": "src",
                    "skipLibCheck": true,
                    "sourceMap": false,
                    "strictNullChecks": false,
                    "target": "es5",
                  },
                  "experimentalWatchApi": true,
                  "transpileOnly": true,
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.tsx\\?\\$/,
      },
      Object {
        "test": /\\\\\\.mjsx\\?\\$/,
        "type": "javascript/auto",
      },
    ],
    "strictExportPresence": false,
    "unknownContextCritical": false,
  },
  "node": Object {
    "child_process": "empty",
    "cluster": "empty",
    "dgram": "empty",
    "dns": "empty",
    "fs": "empty",
    "module": "empty",
    "net": "empty",
    "readline": "empty",
    "repl": "empty",
    "tls": "empty",
  },
  "optimization": Object {
    "concatenateModules": true,
    "flagIncludedChunks": true,
    "mergeDuplicateChunks": true,
    "minimize": false,
    "namedChunks": false,
    "namedModules": false,
    "noEmitOnErrors": true,
    "occurrenceOrder": true,
    "providedExports": true,
    "removeAvailableModules": true,
    "removeEmptyChunks": true,
    "runtimeChunk": false,
    "sideEffects": false,
    "splitChunks": false,
    "usedExports": true,
  },
  "plugins": Array [
    DefinePlugin {
      "definitions": Object {
        "APPLICATION_VERSION": "\\"v3.1.0\\"",
        "VERSION": "\\"3.1.0\\"",
        "__DEV__": false,
        "process.env.ASSET_PATH": "\\"./\\"",
        "process.env.NODE_ENV": "\\"test\\"",
        "process_env_ASSET_PATH": "\\"./\\"",
        "process_env_NODE_ENV": "\\"test\\"",
      },
    },
    CaseSensitivePathsPlugin {
      "fsOperations": 0,
      "logger": BufferedConsole {
        "_buffer": Array [
          Object {
            "message": "⬡ HOLLOW: [1m[34mNODE_ENV:test

[39m[22m",
            "origin": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/loglevelnext/lib/factory/PrefixFactory.js:53",
            "type": "info",
          },
        ],
        "_counters": Object {},
        "_getSourceMaps": [Function],
        "_groupDepth": 0,
        "_timers": Object {},
        "assert": [Function],
        "clear": [Function],
        "count": [Function],
        "countReset": [Function],
        "debug": [Function],
        "dir": [Function],
        "dirxml": [Function],
        "error": [Function],
        "group": [Function],
        "groupCollapsed": [Function],
        "groupEnd": [Function],
        "info": [Function],
        "log": [Function],
        "table": [Function],
        "time": [Function],
        "timeEnd": [Function],
        "timeLog": [Function],
        "trace": [Function],
        "warn": [Function],
      },
      "options": Object {},
      "pathCache": Object {},
      "primed": false,
    },
    IgnorePlugin {
      "checkIgnore": [Function],
      "options": Object {
        "contextRegExp": /moment\\$/,
        "resourceRegExp": /\\^\\\\\\.\\\\/locale\\$/,
      },
    },
    ReplacerWebpackPlugin {
      "options": Object {
        "includes": Array [
          /\\.\\+\\\\\\.css\\$/i,
        ],
        "replace": "url(./$1)",
        "search": /url\\\\\\(\\.\\+\\?\\(static\\\\/media\\\\/\\.\\+\\?\\)\\\\\\)/g,
      },
    },
    MiniCssExtractPlugin {
      "options": Object {
        "chunkFilename": "[name]-[contenthash].chunk.css",
        "filename": "[name]-[contenthash].css",
        "ignoreOrder": false,
        "moduleFilename": [Function],
      },
    },
  ],
  "resolve": Object {
    "alias": Object {
      "@babel/runtime": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/@babel/runtime",
      "babel-core": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/babel-core",
      "babel-runtime": "@babel/runtime",
      "babel-runtime/core-js": "@babel/runtime-corejs3/core-js",
    },
    "extensions": Array [
      ".web.tsx",
      ".web.ts",
      ".web.jsx",
      ".web.js",
      ".ts",
      ".tsx",
      ".js",
      ".jsx",
      ".json",
      ".json5",
      ".worker.js",
      ".worker.jsx",
      ".mjs",
      ".mjsx",
    ],
    "modules": Array [
      "node_modules",
    ],
  },
  "stats": Object {
    "warningsFilter": /export \\.\\* was not found in/,
  },
}
`;

exports[`should support patch.htmls.2 1`] = `
Object {
  "browserslist": Array [
    "last 2 versions",
    "Firefox >= 33",
    "> 1%",
    "IE >= 11",
    "iOS >= 8",
    "Android >= 4",
    "chrome >= 39",
    "Edge >= 12",
    "Safari >= 9",
  ],
  "compilertarget": "es5",
  "htmls": Object {
    "*": Object {
      "a": "1111.hbs",
      "i": "1111.icon",
    },
  },
}
`;

exports[`should support patch.htmls.2 2`] = `
Object {
  "context": "/Users/<USER>/Repositories/torpedo/whispered-build",
  "devServer": Object {
    "clientLogLevel": "none",
    "compress": true,
    "contentBase": "/Users/<USER>/Repositories/torpedo/whispered-build",
    "disableHostCheck": true,
    "headers": Object {
      "Access-Control-Allow-Origin": "*",
    },
    "historyApiFallback": Object {
      "disableDotRule": true,
    },
    "hot": true,
    "hotOnly": true,
    "overlay": false,
    "progress": true,
    "quiet": false,
    "watchContentBase": false,
  },
  "devtool": "source-map",
  "mode": "test",
  "module": Object {
    "noParse": Array [
      /moment\\$/,
    ],
    "rules": Array [
      Object {
        "parser": Object {
          "requireEnsure": false,
          "system": false,
        },
      },
      Object {
        "test": /\\\\\\.svgx\\$/,
        "use": Array [
          Object {
            "loader": "@svgr/webpack",
          },
        ],
      },
      Object {
        "test": /\\\\\\.json5\\$/,
        "use": Array [
          Object {
            "loader": "json5-loader",
          },
        ],
      },
      Object {
        "test": /\\\\\\.\\(woff\\|woff2\\)\\?\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "application/font-woff",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.ttf\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "application/octet-stream",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.eot\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "application/vnd.ms-fontobject",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.svg\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/,
        "use": Array [
          Object {
            "loader": "url-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "mimetype": "image/svg+xml",
              "name": "static/media/[name].[hash].[ext]",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.\\(bmp\\|png\\|jpe\\?g\\|gif\\)\\(\\\\\\?v=\\\\d\\+\\\\\\.\\\\d\\+\\\\\\.\\\\d\\+\\)\\?\\$/i,
        "use": Array [
          Object {
            "loader": "url-loader",
            "options": Object {
              "esModule": false,
              "limit": 10240,
              "name": "static/media/[name].[hash].[ext]",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.html\\?\\$/,
        "use": Array [
          Object {
            "loader": "file-loader",
            "options": Object {
              "esModule": false,
              "name": "static/html/[name].[hash].[ext]",
            },
          },
        ],
      },
      Object {
        "test": /\\\\\\.hbs\\?\\$/,
        "use": Array [
          Object {
            "loader": "mustache-loader",
            "options": Object {
              "render": Object {
                "process_env_ASSET_PATH": "./",
              },
            },
          },
        ],
      },
      Object {
        "oneOf": Array [
          Object {
            "resource": /module\\\\\\.\\\\w\\+ss\\$/,
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": Object {
                    "localIdentName": "[local]--[hash:base64:8]",
                  },
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": false,
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.css\\$/,
      },
      Object {
        "oneOf": Array [
          Object {
            "resource": /module\\\\\\.\\\\w\\+ss\\$/,
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": Object {
                    "localIdentName": "[local]--[hash:base64:8]",
                  },
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "less-loader",
                "options": Object {
                  "javascriptEnabled": true,
                  "modifyVars": Object {},
                  "sourceMap": true,
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js",
              },
              Object {
                "loader": "css-loader",
                "options": Object {
                  "modules": false,
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "postcss-loader",
                "options": Object {
                  "plugins": Array [
                    [Function],
                    [Function],
                  ],
                  "sourceMap": false,
                },
              },
              Object {
                "loader": "less-loader",
                "options": Object {
                  "javascriptEnabled": true,
                  "modifyVars": Object {},
                  "sourceMap": true,
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.less\\$/,
      },
      Object {
        "exclude": Array [
          /node_modules/,
          /assets\\(-dev\\)\\?/,
        ],
        "oneOf": Array [
          Object {
            "resource": /worker\\\\\\.\\[tj\\]sx\\?\\$/,
            "use": Array [
              Object {
                "loader": "workerize-loader",
              },
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.jsx\\?\\$/,
      },
      Object {
        "exclude": Array [
          /node_modules/,
          /assets\\(-dev\\)\\?/,
        ],
        "oneOf": Array [
          Object {
            "resource": /worker\\\\\\.\\[tj\\]sx\\?\\$/,
            "use": Array [
              Object {
                "loader": "workerize-loader",
              },
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
              Object {
                "loader": "ts-loader",
                "options": Object {
                  "compilerOptions": Object {
                    "allowSyntheticDefaultImports": true,
                    "declaration": true,
                    "emitDecoratorMetadata": true,
                    "esModuleInterop": true,
                    "experimentalDecorators": true,
                    "importHelpers": true,
                    "inlineSourceMap": false,
                    "jsx": "react",
                    "lib": Array [
                      "dom",
                      "es5",
                      "es6",
                      "es7",
                      "es2015.promise",
                      "es2018.promise",
                      "es2015.collection",
                      "es2015.core",
                      "es2015",
                      "es2016",
                      "es2016.array.include",
                      "es2017",
                      "es2017.object",
                      "es2018",
                      "es2015.iterable",
                    ],
                    "module": "ESNEXT",
                    "moduleResolution": "node",
                    "noEmitOnError": true,
                    "noFallthroughCasesInSwitch": true,
                    "noImplicitAny": true,
                    "noImplicitReturns": true,
                    "outDir": "dist/lib",
                    "removeComments": true,
                    "rootDir": "src",
                    "skipLibCheck": true,
                    "sourceMap": false,
                    "strictNullChecks": false,
                    "target": "es5",
                  },
                  "experimentalWatchApi": true,
                  "transpileOnly": true,
                },
              },
            ],
          },
          Object {
            "use": Array [
              Object {
                "loader": "babel-loader",
                "options": Object {
                  "babelrc": false,
                  "cacheDirectory": true,
                  "highlightCode": true,
                  "plugins": Array [
                    "@babel/plugin-external-helpers",
                    "@babel/plugin-transform-runtime",
                    "@babel/plugin-transform-object-assign",
                    "@babel/plugin-syntax-dynamic-import",
                    "@babel/plugin-proposal-async-generator-functions",
                    "@babel/plugin-transform-regenerator",
                    "@babel/plugin-proposal-function-bind",
                    "@babel/plugin-proposal-object-rest-spread",
                    Array [
                      "@babel/plugin-proposal-decorators",
                      Object {
                        "legacy": true,
                      },
                    ],
                    Array [
                      "@babel/plugin-proposal-class-properties",
                      Object {
                        "loose": true,
                      },
                    ],
                  ],
                  "presets": Array [
                    Array [
                      "@babel/preset-env",
                      Object {
                        "modules": false,
                        "targets": Object {
                          "browsers": Array [
                            "last 2 versions",
                            "Firefox >= 33",
                            "> 1%",
                            "IE >= 11",
                            "iOS >= 8",
                            "Android >= 4",
                            "chrome >= 39",
                            "Edge >= 12",
                            "Safari >= 9",
                          ],
                        },
                      },
                    ],
                    "@babel/preset-react",
                  ],
                },
              },
              Object {
                "loader": "ts-loader",
                "options": Object {
                  "compilerOptions": Object {
                    "allowSyntheticDefaultImports": true,
                    "declaration": true,
                    "emitDecoratorMetadata": true,
                    "esModuleInterop": true,
                    "experimentalDecorators": true,
                    "importHelpers": true,
                    "inlineSourceMap": false,
                    "jsx": "react",
                    "lib": Array [
                      "dom",
                      "es5",
                      "es6",
                      "es7",
                      "es2015.promise",
                      "es2018.promise",
                      "es2015.collection",
                      "es2015.core",
                      "es2015",
                      "es2016",
                      "es2016.array.include",
                      "es2017",
                      "es2017.object",
                      "es2018",
                      "es2015.iterable",
                    ],
                    "module": "ESNEXT",
                    "moduleResolution": "node",
                    "noEmitOnError": true,
                    "noFallthroughCasesInSwitch": true,
                    "noImplicitAny": true,
                    "noImplicitReturns": true,
                    "outDir": "dist/lib",
                    "removeComments": true,
                    "rootDir": "src",
                    "skipLibCheck": true,
                    "sourceMap": false,
                    "strictNullChecks": false,
                    "target": "es5",
                  },
                  "experimentalWatchApi": true,
                  "transpileOnly": true,
                },
              },
            ],
          },
        ],
        "test": /\\\\\\.tsx\\?\\$/,
      },
      Object {
        "test": /\\\\\\.mjsx\\?\\$/,
        "type": "javascript/auto",
      },
    ],
    "strictExportPresence": false,
    "unknownContextCritical": false,
  },
  "node": Object {
    "child_process": "empty",
    "cluster": "empty",
    "dgram": "empty",
    "dns": "empty",
    "fs": "empty",
    "module": "empty",
    "net": "empty",
    "readline": "empty",
    "repl": "empty",
    "tls": "empty",
  },
  "optimization": Object {
    "concatenateModules": true,
    "flagIncludedChunks": true,
    "mergeDuplicateChunks": true,
    "minimize": false,
    "namedChunks": false,
    "namedModules": false,
    "noEmitOnErrors": true,
    "occurrenceOrder": true,
    "providedExports": true,
    "removeAvailableModules": true,
    "removeEmptyChunks": true,
    "runtimeChunk": false,
    "sideEffects": false,
    "splitChunks": false,
    "usedExports": true,
  },
  "plugins": Array [
    DefinePlugin {
      "definitions": Object {
        "APPLICATION_VERSION": "\\"v3.1.0\\"",
        "VERSION": "\\"3.1.0\\"",
        "__DEV__": false,
        "process.env.ASSET_PATH": "\\"./\\"",
        "process.env.NODE_ENV": "\\"test\\"",
        "process_env_ASSET_PATH": "\\"./\\"",
        "process_env_NODE_ENV": "\\"test\\"",
      },
    },
    CaseSensitivePathsPlugin {
      "fsOperations": 0,
      "logger": BufferedConsole {
        "_buffer": Array [
          Object {
            "message": "⬡ HOLLOW: [1m[34mNODE_ENV:test

[39m[22m",
            "origin": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/loglevelnext/lib/factory/PrefixFactory.js:53",
            "type": "info",
          },
        ],
        "_counters": Object {},
        "_getSourceMaps": [Function],
        "_groupDepth": 0,
        "_timers": Object {},
        "assert": [Function],
        "clear": [Function],
        "count": [Function],
        "countReset": [Function],
        "debug": [Function],
        "dir": [Function],
        "dirxml": [Function],
        "error": [Function],
        "group": [Function],
        "groupCollapsed": [Function],
        "groupEnd": [Function],
        "info": [Function],
        "log": [Function],
        "table": [Function],
        "time": [Function],
        "timeEnd": [Function],
        "timeLog": [Function],
        "trace": [Function],
        "warn": [Function],
      },
      "options": Object {},
      "pathCache": Object {},
      "primed": false,
    },
    IgnorePlugin {
      "checkIgnore": [Function],
      "options": Object {
        "contextRegExp": /moment\\$/,
        "resourceRegExp": /\\^\\\\\\.\\\\/locale\\$/,
      },
    },
    ReplacerWebpackPlugin {
      "options": Object {
        "includes": Array [
          /\\.\\+\\\\\\.css\\$/i,
        ],
        "replace": "url(./$1)",
        "search": /url\\\\\\(\\.\\+\\?\\(static\\\\/media\\\\/\\.\\+\\?\\)\\\\\\)/g,
      },
    },
    MiniCssExtractPlugin {
      "options": Object {
        "chunkFilename": "[name]-[contenthash].chunk.css",
        "filename": "[name]-[contenthash].css",
        "ignoreOrder": false,
        "moduleFilename": [Function],
      },
    },
  ],
  "resolve": Object {
    "alias": Object {
      "@babel/runtime": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/@babel/runtime",
      "babel-core": "/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/babel-core",
      "babel-runtime": "@babel/runtime",
      "babel-runtime/core-js": "@babel/runtime-corejs3/core-js",
    },
    "extensions": Array [
      ".web.tsx",
      ".web.ts",
      ".web.jsx",
      ".web.js",
      ".ts",
      ".tsx",
      ".js",
      ".jsx",
      ".json",
      ".json5",
      ".worker.js",
      ".worker.jsx",
      ".mjs",
      ".mjsx",
    ],
    "modules": Array [
      "node_modules",
    ],
  },
  "stats": Object {
    "warningsFilter": /export \\.\\* was not found in/,
  },
}
`;
