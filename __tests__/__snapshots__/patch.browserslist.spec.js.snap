// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`should support patch.browserslist 1`] = `
"{
  mode: 'test',
  context: '/Users/<USER>/Repositories/torpedo/whispered-build',
  stats: {
    warningsFilter: /export .* was not found in/
  },
  devtool: 'source-map',
  node: {
    child_process: 'empty',
    cluster: 'empty',
    dgram: 'empty',
    dns: 'empty',
    fs: 'empty',
    module: 'empty',
    net: 'empty',
    readline: 'empty',
    repl: 'empty',
    tls: 'empty'
  },
  resolve: {
    alias: {
      '@babel/runtime': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/@babel/runtime',
      'babel-core': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/babel-core',
      'babel-runtime/core-js': '@babel/runtime-corejs3/core-js',
      'babel-runtime': '@babel/runtime'
    },
    extensions: [
      '.web.tsx',
      '.web.ts',
      '.web.jsx',
      '.web.js',
      '.ts',
      '.tsx',
      '.js',
      '.jsx',
      '.json',
      '.json5',
      '.worker.js',
      '.worker.jsx',
      '.mjs',
      '.mjsx'
    ],
    modules: [
      'node_modules'
    ]
  },
  devServer: {
    hot: true,
    hotOnly: true,
    contentBase: '/Users/<USER>/Repositories/torpedo/whispered-build',
    watchContentBase: false,
    compress: true,
    progress: true,
    quiet: false,
    disableHostCheck: true,
    clientLogLevel: 'none',
    overlay: false,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    historyApiFallback: {
      disableDotRule: true
    }
  },
  module: {
    unknownContextCritical: false,
    strictExportPresence: false,
    noParse: [
      /moment$/
    ],
    rules: [
      /* config.module.rule('parser') */
      {
        parser: {
          requireEnsure: false,
          system: false
        }
      },
      /* config.module.rule('svgx') */
      {
        test: /\\\\.svgx$/,
        use: [
          /* config.module.rule('svgx').use('svgx') */
          {
            loader: '@svgr/webpack'
          }
        ]
      },
      /* config.module.rule('json5') */
      {
        test: /\\\\.json5$/,
        use: [
          /* config.module.rule('json5').use('json5') */
          {
            loader: 'json5-loader'
          }
        ]
      },
      /* config.module.rule('woff') */
      {
        test: /\\\\.(woff|woff2)?(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('woff').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/font-woff'
            }
          }
        ]
      },
      /* config.module.rule('ttf') */
      {
        test: /\\\\.ttf(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('ttf').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/octet-stream'
            }
          }
        ]
      },
      /* config.module.rule('eot') */
      {
        test: /\\\\.eot(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('eot').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/vnd.ms-fontobject'
            }
          }
        ]
      },
      /* config.module.rule('svg') */
      {
        test: /\\\\.svg(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('svg').use('svg') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]',
              mimetype: 'image/svg+xml'
            }
          }
        ]
      },
      /* config.module.rule('img') */
      {
        test: /\\\\.(bmp|png|jpe?g|gif)(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/i,
        use: [
          /* config.module.rule('img').use('img') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('html') */
      {
        test: /\\\\.html?$/,
        use: [
          /* config.module.rule('html').use('html') */
          {
            loader: 'file-loader',
            options: {
              esModule: false,
              name: 'static/html/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('hbs') */
      {
        test: /\\\\.hbs?$/,
        use: [
          /* config.module.rule('hbs').use('hbs') */
          {
            loader: 'mustache-loader',
            options: {
              render: {
                process_env_ASSET_PATH: './'
              }
            }
          }
        ]
      },
      /* config.module.rule('css') */
      {
        test: /\\\\.css$/,
        oneOf: [
          /* config.module.rule('css').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('css').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('css').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          },
          /* config.module.rule('css').oneOf('normal') */
          {
            use: [
              /* config.module.rule('css').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('css').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('less') */
      {
        test: /\\\\.less$/,
        oneOf: [
          /* config.module.rule('less').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('less').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('less').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('modules').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          },
          /* config.module.rule('less').oneOf('normal') */
          {
            use: [
              /* config.module.rule('less').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('jsx') */
      {
        test: /\\\\.jsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('jsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('jsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('jsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          },
          /* config.module.rule('jsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('jsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('tsx') */
      {
        test: /\\\\.tsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('tsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('tsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('tsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('workers').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es5',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          },
          /* config.module.rule('tsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('tsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('normal').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es5',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('mjsx') */
      {
        test: /\\\\.mjsx?$/,
        type: 'javascript/auto'
      }
    ]
  },
  optimization: {
    minimize: false,
    splitChunks: false,
    runtimeChunk: false,
    removeAvailableModules: true,
    removeEmptyChunks: true,
    mergeDuplicateChunks: true,
    sideEffects: false,
    flagIncludedChunks: true,
    occurrenceOrder: true,
    concatenateModules: true,
    usedExports: true,
    providedExports: true,
    noEmitOnErrors: true,
    namedModules: false,
    namedChunks: false
  },
  plugins: [
    /* config.plugin('DefinePlugin') */
    new DefinePlugin(
      {
        __DEV__: false,
        VERSION: '\\"3.1.0\\"',
        APPLICATION_VERSION: '\\"v3.1.0\\"',
        'process.env.NODE_ENV': '\\"test\\"',
        'process.env.ASSET_PATH': '\\"./\\"',
        process_env_NODE_ENV: '\\"test\\"',
        process_env_ASSET_PATH: '\\"./\\"'
      }
    ),
    /* config.plugin('CaseSensitivePathsPlugin') */
    new CaseSensitivePathsPlugin(),
    /* config.plugin('IgnorePlugin') */
    new IgnorePlugin(
      /^\\\\.\\\\/locale$/,
      /moment$/
    ),
    /* config.plugin('ReplacerWebpackPlugin') */
    new ReplacerWebpackPlugin(
      {
        includes: [
          /.+\\\\.css$/i
        ],
        search: /url\\\\(.+?(static\\\\/media\\\\/.+?)\\\\)/g,
        replace: 'url(./$1)'
      }
    ),
    /* config.plugin('MiniCSSExtractPlugin') */
    new MiniCssExtractPlugin(
      {
        filename: '[name]-[contenthash].css',
        chunkFilename: '[name]-[contenthash].chunk.css'
      }
    )
  ]
}"
`;

exports[`should support patch.browserslist:es:low 1`] = `
"{
  mode: 'test',
  context: '/Users/<USER>/Repositories/torpedo/whispered-build',
  stats: {
    warningsFilter: /export .* was not found in/
  },
  devtool: 'source-map',
  node: {
    child_process: 'empty',
    cluster: 'empty',
    dgram: 'empty',
    dns: 'empty',
    fs: 'empty',
    module: 'empty',
    net: 'empty',
    readline: 'empty',
    repl: 'empty',
    tls: 'empty'
  },
  resolve: {
    alias: {
      '@babel/runtime': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/@babel/runtime',
      'babel-core': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/babel-core',
      'babel-runtime/core-js': '@babel/runtime-corejs3/core-js',
      'babel-runtime': '@babel/runtime'
    },
    extensions: [
      '.web.tsx',
      '.web.ts',
      '.web.jsx',
      '.web.js',
      '.ts',
      '.tsx',
      '.js',
      '.jsx',
      '.json',
      '.json5',
      '.worker.js',
      '.worker.jsx',
      '.mjs',
      '.mjsx'
    ],
    modules: [
      'node_modules'
    ]
  },
  devServer: {
    hot: true,
    hotOnly: true,
    contentBase: '/Users/<USER>/Repositories/torpedo/whispered-build',
    watchContentBase: false,
    compress: true,
    progress: true,
    quiet: false,
    disableHostCheck: true,
    clientLogLevel: 'none',
    overlay: false,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    historyApiFallback: {
      disableDotRule: true
    }
  },
  module: {
    unknownContextCritical: false,
    strictExportPresence: false,
    noParse: [
      /moment$/
    ],
    rules: [
      /* config.module.rule('parser') */
      {
        parser: {
          requireEnsure: false,
          system: false
        }
      },
      /* config.module.rule('svgx') */
      {
        test: /\\\\.svgx$/,
        use: [
          /* config.module.rule('svgx').use('svgx') */
          {
            loader: '@svgr/webpack'
          }
        ]
      },
      /* config.module.rule('json5') */
      {
        test: /\\\\.json5$/,
        use: [
          /* config.module.rule('json5').use('json5') */
          {
            loader: 'json5-loader'
          }
        ]
      },
      /* config.module.rule('woff') */
      {
        test: /\\\\.(woff|woff2)?(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('woff').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/font-woff'
            }
          }
        ]
      },
      /* config.module.rule('ttf') */
      {
        test: /\\\\.ttf(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('ttf').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/octet-stream'
            }
          }
        ]
      },
      /* config.module.rule('eot') */
      {
        test: /\\\\.eot(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('eot').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/vnd.ms-fontobject'
            }
          }
        ]
      },
      /* config.module.rule('svg') */
      {
        test: /\\\\.svg(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('svg').use('svg') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]',
              mimetype: 'image/svg+xml'
            }
          }
        ]
      },
      /* config.module.rule('img') */
      {
        test: /\\\\.(bmp|png|jpe?g|gif)(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/i,
        use: [
          /* config.module.rule('img').use('img') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('html') */
      {
        test: /\\\\.html?$/,
        use: [
          /* config.module.rule('html').use('html') */
          {
            loader: 'file-loader',
            options: {
              esModule: false,
              name: 'static/html/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('hbs') */
      {
        test: /\\\\.hbs?$/,
        use: [
          /* config.module.rule('hbs').use('hbs') */
          {
            loader: 'mustache-loader',
            options: {
              render: {
                process_env_ASSET_PATH: './'
              }
            }
          }
        ]
      },
      /* config.module.rule('css') */
      {
        test: /\\\\.css$/,
        oneOf: [
          /* config.module.rule('css').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('css').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('css').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          },
          /* config.module.rule('css').oneOf('normal') */
          {
            use: [
              /* config.module.rule('css').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('css').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('less') */
      {
        test: /\\\\.less$/,
        oneOf: [
          /* config.module.rule('less').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('less').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('less').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('modules').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          },
          /* config.module.rule('less').oneOf('normal') */
          {
            use: [
              /* config.module.rule('less').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('jsx') */
      {
        test: /\\\\.jsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('jsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('jsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('jsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome < 39'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          },
          /* config.module.rule('jsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('jsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome < 39'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('tsx') */
      {
        test: /\\\\.tsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('tsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('tsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('tsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome < 39'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('workers').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es5',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          },
          /* config.module.rule('tsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('tsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome < 39'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('normal').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es5',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('mjsx') */
      {
        test: /\\\\.mjsx?$/,
        type: 'javascript/auto'
      }
    ]
  },
  optimization: {
    minimize: false,
    splitChunks: false,
    runtimeChunk: false,
    removeAvailableModules: true,
    removeEmptyChunks: true,
    mergeDuplicateChunks: true,
    sideEffects: false,
    flagIncludedChunks: true,
    occurrenceOrder: true,
    concatenateModules: true,
    usedExports: true,
    providedExports: true,
    noEmitOnErrors: true,
    namedModules: false,
    namedChunks: false
  },
  plugins: [
    /* config.plugin('DefinePlugin') */
    new DefinePlugin(
      {
        __DEV__: false,
        VERSION: '\\"3.1.0\\"',
        APPLICATION_VERSION: '\\"v3.1.0\\"',
        'process.env.NODE_ENV': '\\"test\\"',
        'process.env.ASSET_PATH': '\\"./\\"',
        process_env_NODE_ENV: '\\"test\\"',
        process_env_ASSET_PATH: '\\"./\\"'
      }
    ),
    /* config.plugin('CaseSensitivePathsPlugin') */
    new CaseSensitivePathsPlugin(),
    /* config.plugin('IgnorePlugin') */
    new IgnorePlugin(
      /^\\\\.\\\\/locale$/,
      /moment$/
    ),
    /* config.plugin('ReplacerWebpackPlugin') */
    new ReplacerWebpackPlugin(
      {
        includes: [
          /.+\\\\.css$/i
        ],
        search: /url\\\\(.+?(static\\\\/media\\\\/.+?)\\\\)/g,
        replace: 'url(./$1)'
      }
    ),
    /* config.plugin('MiniCSSExtractPlugin') */
    new MiniCssExtractPlugin(
      {
        filename: '[name]-[contenthash].css',
        chunkFilename: '[name]-[contenthash].chunk.css'
      }
    )
  ]
}"
`;

exports[`should support patch.browserslist:es6 1`] = `
"{
  mode: 'test',
  context: '/Users/<USER>/Repositories/torpedo/whispered-build',
  stats: {
    warningsFilter: /export .* was not found in/
  },
  devtool: 'source-map',
  node: {
    child_process: 'empty',
    cluster: 'empty',
    dgram: 'empty',
    dns: 'empty',
    fs: 'empty',
    module: 'empty',
    net: 'empty',
    readline: 'empty',
    repl: 'empty',
    tls: 'empty'
  },
  resolve: {
    alias: {
      '@babel/runtime': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/@babel/runtime',
      'babel-core': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/babel-core',
      'babel-runtime/core-js': '@babel/runtime-corejs3/core-js',
      'babel-runtime': '@babel/runtime'
    },
    extensions: [
      '.web.tsx',
      '.web.ts',
      '.web.jsx',
      '.web.js',
      '.ts',
      '.tsx',
      '.js',
      '.jsx',
      '.json',
      '.json5',
      '.worker.js',
      '.worker.jsx',
      '.mjs',
      '.mjsx'
    ],
    modules: [
      'node_modules'
    ]
  },
  devServer: {
    hot: true,
    hotOnly: true,
    contentBase: '/Users/<USER>/Repositories/torpedo/whispered-build',
    watchContentBase: false,
    compress: true,
    progress: true,
    quiet: false,
    disableHostCheck: true,
    clientLogLevel: 'none',
    overlay: false,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    historyApiFallback: {
      disableDotRule: true
    }
  },
  module: {
    unknownContextCritical: false,
    strictExportPresence: false,
    noParse: [
      /moment$/
    ],
    rules: [
      /* config.module.rule('parser') */
      {
        parser: {
          requireEnsure: false,
          system: false
        }
      },
      /* config.module.rule('svgx') */
      {
        test: /\\\\.svgx$/,
        use: [
          /* config.module.rule('svgx').use('svgx') */
          {
            loader: '@svgr/webpack'
          }
        ]
      },
      /* config.module.rule('json5') */
      {
        test: /\\\\.json5$/,
        use: [
          /* config.module.rule('json5').use('json5') */
          {
            loader: 'json5-loader'
          }
        ]
      },
      /* config.module.rule('woff') */
      {
        test: /\\\\.(woff|woff2)?(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('woff').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/font-woff'
            }
          }
        ]
      },
      /* config.module.rule('ttf') */
      {
        test: /\\\\.ttf(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('ttf').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/octet-stream'
            }
          }
        ]
      },
      /* config.module.rule('eot') */
      {
        test: /\\\\.eot(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('eot').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/vnd.ms-fontobject'
            }
          }
        ]
      },
      /* config.module.rule('svg') */
      {
        test: /\\\\.svg(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('svg').use('svg') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]',
              mimetype: 'image/svg+xml'
            }
          }
        ]
      },
      /* config.module.rule('img') */
      {
        test: /\\\\.(bmp|png|jpe?g|gif)(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/i,
        use: [
          /* config.module.rule('img').use('img') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('html') */
      {
        test: /\\\\.html?$/,
        use: [
          /* config.module.rule('html').use('html') */
          {
            loader: 'file-loader',
            options: {
              esModule: false,
              name: 'static/html/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('hbs') */
      {
        test: /\\\\.hbs?$/,
        use: [
          /* config.module.rule('hbs').use('hbs') */
          {
            loader: 'mustache-loader',
            options: {
              render: {
                process_env_ASSET_PATH: './'
              }
            }
          }
        ]
      },
      /* config.module.rule('css') */
      {
        test: /\\\\.css$/,
        oneOf: [
          /* config.module.rule('css').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('css').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('css').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          },
          /* config.module.rule('css').oneOf('normal') */
          {
            use: [
              /* config.module.rule('css').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('css').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('less') */
      {
        test: /\\\\.less$/,
        oneOf: [
          /* config.module.rule('less').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('less').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('less').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('modules').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          },
          /* config.module.rule('less').oneOf('normal') */
          {
            use: [
              /* config.module.rule('less').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('jsx') */
      {
        test: /\\\\.jsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('jsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('jsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('jsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          },
          /* config.module.rule('jsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('jsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('tsx') */
      {
        test: /\\\\.tsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('tsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('tsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('tsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('workers').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es6',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          },
          /* config.module.rule('tsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('tsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'last 2 Chrome versions',
                            'not Chrome < 60',
                            'last 2 Safari versions',
                            'not Safari < 10.1',
                            'last 2 iOS versions',
                            'not iOS < 10.3',
                            'last 2 Firefox versions',
                            'not Firefox < 54',
                            'last 2 Edge versions',
                            'not Edge < 15'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('normal').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es6',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('mjsx') */
      {
        test: /\\\\.mjsx?$/,
        type: 'javascript/auto'
      }
    ]
  },
  optimization: {
    minimize: false,
    splitChunks: false,
    runtimeChunk: false,
    removeAvailableModules: true,
    removeEmptyChunks: true,
    mergeDuplicateChunks: true,
    sideEffects: false,
    flagIncludedChunks: true,
    occurrenceOrder: true,
    concatenateModules: true,
    usedExports: true,
    providedExports: true,
    noEmitOnErrors: true,
    namedModules: false,
    namedChunks: false
  },
  plugins: [
    /* config.plugin('DefinePlugin') */
    new DefinePlugin(
      {
        __DEV__: false,
        VERSION: '\\"3.1.0\\"',
        APPLICATION_VERSION: '\\"v3.1.0\\"',
        'process.env.NODE_ENV': '\\"test\\"',
        'process.env.ASSET_PATH': '\\"./\\"',
        process_env_NODE_ENV: '\\"test\\"',
        process_env_ASSET_PATH: '\\"./\\"'
      }
    ),
    /* config.plugin('CaseSensitivePathsPlugin') */
    new CaseSensitivePathsPlugin(),
    /* config.plugin('IgnorePlugin') */
    new IgnorePlugin(
      /^\\\\.\\\\/locale$/,
      /moment$/
    ),
    /* config.plugin('ReplacerWebpackPlugin') */
    new ReplacerWebpackPlugin(
      {
        includes: [
          /.+\\\\.css$/i
        ],
        search: /url\\\\(.+?(static\\\\/media\\\\/.+?)\\\\)/g,
        replace: 'url(./$1)'
      }
    ),
    /* config.plugin('MiniCSSExtractPlugin') */
    new MiniCssExtractPlugin(
      {
        filename: '[name]-[contenthash].css',
        chunkFilename: '[name]-[contenthash].chunk.css'
      }
    )
  ]
}"
`;

exports[`should support patch.browserslist:esx 1`] = `
"{
  mode: 'test',
  context: '/Users/<USER>/Repositories/torpedo/whispered-build',
  stats: {
    warningsFilter: /export .* was not found in/
  },
  devtool: 'source-map',
  node: {
    child_process: 'empty',
    cluster: 'empty',
    dgram: 'empty',
    dns: 'empty',
    fs: 'empty',
    module: 'empty',
    net: 'empty',
    readline: 'empty',
    repl: 'empty',
    tls: 'empty'
  },
  resolve: {
    alias: {
      '@babel/runtime': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/@babel/runtime',
      'babel-core': '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/babel-core',
      'babel-runtime/core-js': '@babel/runtime-corejs3/core-js',
      'babel-runtime': '@babel/runtime'
    },
    extensions: [
      '.web.tsx',
      '.web.ts',
      '.web.jsx',
      '.web.js',
      '.ts',
      '.tsx',
      '.js',
      '.jsx',
      '.json',
      '.json5',
      '.worker.js',
      '.worker.jsx',
      '.mjs',
      '.mjsx'
    ],
    modules: [
      'node_modules'
    ]
  },
  devServer: {
    hot: true,
    hotOnly: true,
    contentBase: '/Users/<USER>/Repositories/torpedo/whispered-build',
    watchContentBase: false,
    compress: true,
    progress: true,
    quiet: false,
    disableHostCheck: true,
    clientLogLevel: 'none',
    overlay: false,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    historyApiFallback: {
      disableDotRule: true
    }
  },
  module: {
    unknownContextCritical: false,
    strictExportPresence: false,
    noParse: [
      /moment$/
    ],
    rules: [
      /* config.module.rule('parser') */
      {
        parser: {
          requireEnsure: false,
          system: false
        }
      },
      /* config.module.rule('svgx') */
      {
        test: /\\\\.svgx$/,
        use: [
          /* config.module.rule('svgx').use('svgx') */
          {
            loader: '@svgr/webpack'
          }
        ]
      },
      /* config.module.rule('json5') */
      {
        test: /\\\\.json5$/,
        use: [
          /* config.module.rule('json5').use('json5') */
          {
            loader: 'json5-loader'
          }
        ]
      },
      /* config.module.rule('woff') */
      {
        test: /\\\\.(woff|woff2)?(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('woff').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/font-woff'
            }
          }
        ]
      },
      /* config.module.rule('ttf') */
      {
        test: /\\\\.ttf(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('ttf').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/octet-stream'
            }
          }
        ]
      },
      /* config.module.rule('eot') */
      {
        test: /\\\\.eot(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('eot').use('font') */
          {
            loader: 'file-loader',
            options: {
              limit: 10240,
              esModule: false,
              mimetype: 'application/vnd.ms-fontobject'
            }
          }
        ]
      },
      /* config.module.rule('svg') */
      {
        test: /\\\\.svg(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/,
        use: [
          /* config.module.rule('svg').use('svg') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]',
              mimetype: 'image/svg+xml'
            }
          }
        ]
      },
      /* config.module.rule('img') */
      {
        test: /\\\\.(bmp|png|jpe?g|gif)(\\\\?v=\\\\d+\\\\.\\\\d+\\\\.\\\\d+)?$/i,
        use: [
          /* config.module.rule('img').use('img') */
          {
            loader: 'url-loader',
            options: {
              limit: 10240,
              esModule: false,
              name: 'static/media/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('html') */
      {
        test: /\\\\.html?$/,
        use: [
          /* config.module.rule('html').use('html') */
          {
            loader: 'file-loader',
            options: {
              esModule: false,
              name: 'static/html/[name].[hash].[ext]'
            }
          }
        ]
      },
      /* config.module.rule('hbs') */
      {
        test: /\\\\.hbs?$/,
        use: [
          /* config.module.rule('hbs').use('hbs') */
          {
            loader: 'mustache-loader',
            options: {
              render: {
                process_env_ASSET_PATH: './'
              }
            }
          }
        ]
      },
      /* config.module.rule('css') */
      {
        test: /\\\\.css$/,
        oneOf: [
          /* config.module.rule('css').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('css').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('css').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          },
          /* config.module.rule('css').oneOf('normal') */
          {
            use: [
              /* config.module.rule('css').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('css').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('css').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('less') */
      {
        test: /\\\\.less$/,
        oneOf: [
          /* config.module.rule('less').oneOf('modules') */
          {
            resource: /module\\\\.\\\\w+ss$/,
            use: [
              /* config.module.rule('less').oneOf('modules').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('modules').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: {
                    localIdentName: '[local]--[hash:base64:8]'
                  }
                }
              },
              /* config.module.rule('less').oneOf('modules').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('modules').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          },
          /* config.module.rule('less').oneOf('normal') */
          {
            use: [
              /* config.module.rule('less').oneOf('normal').use('mini-css-extract') */
              {
                loader: '/Users/<USER>/Repositories/torpedo/whispered-build/node_modules/mini-css-extract-plugin/dist/loader.js'
              },
              /* config.module.rule('less').oneOf('normal').use('css-loader') */
              {
                loader: 'css-loader',
                options: {
                  sourceMap: false,
                  modules: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('postcss-loader') */
              {
                loader: 'postcss-loader',
                options: {
                  plugins: [
                    function () { /* omitted long function */ },
                    function () { /* omitted long function */ }
                  ],
                  sourceMap: false
                }
              },
              /* config.module.rule('less').oneOf('normal').use('less-loader') */
              {
                loader: 'less-loader',
                options: {
                  javascriptEnabled: true,
                  sourceMap: true,
                  modifyVars: {}
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('jsx') */
      {
        test: /\\\\.jsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('jsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('jsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('jsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome >= 70'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          },
          /* config.module.rule('jsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('jsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome >= 70'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('tsx') */
      {
        test: /\\\\.tsx?$/,
        exclude: [
          /node_modules/,
          /assets(-dev)?/
        ],
        oneOf: [
          /* config.module.rule('tsx').oneOf('workers') */
          {
            resource: /worker\\\\.[tj]sx?$/,
            use: [
              /* config.module.rule('tsx').oneOf('workers').use('workerize') */
              {
                loader: 'workerize-loader'
              },
              /* config.module.rule('tsx').oneOf('workers').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome >= 70'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('workers').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es6',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          },
          /* config.module.rule('tsx').oneOf('normal') */
          {
            use: [
              /* config.module.rule('tsx').oneOf('normal').use('babel') */
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                  babelrc: false,
                  highlightCode: true,
                  presets: [
                    [
                      '@babel/preset-env',
                      {
                        modules: false,
                        targets: {
                          browsers: [
                            'Chrome >= 70'
                          ]
                        }
                      }
                    ],
                    '@babel/preset-react'
                  ],
                  plugins: [
                    '@babel/plugin-external-helpers',
                    '@babel/plugin-transform-runtime',
                    '@babel/plugin-transform-object-assign',
                    '@babel/plugin-syntax-dynamic-import',
                    '@babel/plugin-proposal-async-generator-functions',
                    '@babel/plugin-transform-regenerator',
                    '@babel/plugin-proposal-function-bind',
                    '@babel/plugin-proposal-object-rest-spread',
                    [
                      '@babel/plugin-proposal-decorators',
                      {
                        legacy: true
                      }
                    ],
                    [
                      '@babel/plugin-proposal-class-properties',
                      {
                        loose: true
                      }
                    ]
                  ]
                }
              },
              /* config.module.rule('tsx').oneOf('normal').use('ts') */
              {
                loader: 'ts-loader',
                options: {
                  transpileOnly: true,
                  experimentalWatchApi: true,
                  compilerOptions: {
                    importHelpers: true,
                    allowSyntheticDefaultImports: true,
                    sourceMap: false,
                    declaration: true,
                    target: 'es6',
                    module: 'ESNEXT',
                    moduleResolution: 'node',
                    jsx: 'react',
                    esModuleInterop: true,
                    noEmitOnError: true,
                    noFallthroughCasesInSwitch: true,
                    noImplicitAny: true,
                    noImplicitReturns: true,
                    removeComments: true,
                    strictNullChecks: false,
                    inlineSourceMap: false,
                    emitDecoratorMetadata: true,
                    experimentalDecorators: true,
                    outDir: 'dist/lib',
                    rootDir: 'src',
                    skipLibCheck: true,
                    lib: [
                      'dom',
                      'es5',
                      'es6',
                      'es7',
                      'es2015.promise',
                      'es2018.promise',
                      'es2015.collection',
                      'es2015.core',
                      'es2015',
                      'es2016',
                      'es2016.array.include',
                      'es2017',
                      'es2017.object',
                      'es2018',
                      'es2015.iterable'
                    ]
                  }
                }
              }
            ]
          }
        ]
      },
      /* config.module.rule('mjsx') */
      {
        test: /\\\\.mjsx?$/,
        type: 'javascript/auto'
      }
    ]
  },
  optimization: {
    minimize: false,
    splitChunks: false,
    runtimeChunk: false,
    removeAvailableModules: true,
    removeEmptyChunks: true,
    mergeDuplicateChunks: true,
    sideEffects: false,
    flagIncludedChunks: true,
    occurrenceOrder: true,
    concatenateModules: true,
    usedExports: true,
    providedExports: true,
    noEmitOnErrors: true,
    namedModules: false,
    namedChunks: false
  },
  plugins: [
    /* config.plugin('DefinePlugin') */
    new DefinePlugin(
      {
        __DEV__: false,
        VERSION: '\\"3.1.0\\"',
        APPLICATION_VERSION: '\\"v3.1.0\\"',
        'process.env.NODE_ENV': '\\"test\\"',
        'process.env.ASSET_PATH': '\\"./\\"',
        process_env_NODE_ENV: '\\"test\\"',
        process_env_ASSET_PATH: '\\"./\\"'
      }
    ),
    /* config.plugin('CaseSensitivePathsPlugin') */
    new CaseSensitivePathsPlugin(),
    /* config.plugin('IgnorePlugin') */
    new IgnorePlugin(
      /^\\\\.\\\\/locale$/,
      /moment$/
    ),
    /* config.plugin('ReplacerWebpackPlugin') */
    new ReplacerWebpackPlugin(
      {
        includes: [
          /.+\\\\.css$/i
        ],
        search: /url\\\\(.+?(static\\\\/media\\\\/.+?)\\\\)/g,
        replace: 'url(./$1)'
      }
    ),
    /* config.plugin('MiniCSSExtractPlugin') */
    new MiniCssExtractPlugin(
      {
        filename: '[name]-[contenthash].css',
        chunkFilename: '[name]-[contenthash].chunk.css'
      }
    )
  ]
}"
`;
