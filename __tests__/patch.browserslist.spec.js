/***************************************************
 * Created by nany<PERSON><PERSON>feng on 2019/9/17 17:56. *
 ***************************************************/

it('should support patch.browserslist', () => {
  const config = require('../configs')()
  config.patch.browserslist([
    'last 2 Chrome versions',
    'not Chrome < 60',
    'last 2 Safari versions',
    'not Safari < 10.1',
    'last 2 iOS versions',
    'not iOS < 10.3',
    'last 2 Firefox versions',
    'not Firefox < 54',
    'last 2 Edge versions',
    'not Edge < 15'
  ])

  const oo = config.toString()
  expect(oo).toMatchSnapshot()
})

it('should support patch.browserslist:es6', () => {
  const config = require('../configs')()
  config.patch.browserslist('es6')
  const oo = config.toString()
  expect(oo).toMatchSnapshot()
})

it('should support patch.browserslist:esx', () => {
  const config = require('../configs')()
  config.patch.browserslist(['Chrome >= 70'], 'es6')
  const oo = config.toString()
  expect(oo).toMatchSnapshot()
})

it('should support patch.browserslist:es:low', () => {
  const config = require('../configs')()
  config.patch.browserslist(['Chrome < 39'], 'es5')
  const oo = config.toString()
  expect(oo).toMatchSnapshot()
})
