/***************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 2020/7/14 16:01. *
 ***************************************************/

const checkPluginNames = require('../../sdks_for_entry/check-plugins-names')

it('should checked', () => {
  expect(
    checkPluginNames({
      name: '@ekuaibao/entry-web',
      whispered: {
        plugins: {
          reports: ''
        },
        entryPlugins: {
          app: ['layout-app']
        },
        ignorePlugins: ['experience']
      },
      optionalDependencies: {
        '@ekuaibao/plugin-web-demo': '1.6.14-release.3'
      }
    })
  ).toEqual([])

  expect(
    checkPluginNames({
      name: '@ekuaibao/entry-web',
      whispered: {
        plugins: {
          reports: '',
          demo: ''
        },
        ignorePlugins: ['experience']
      },
      optionalDependencies: {
        '@ekuaibao/plugin-web-demo': '1.6.14-release.3'
      }
    })
  ).toEqual(['demo'])

  expect(
    checkPluginNames({
      name: '@ekuaibao/entry-web',
      whispered: {
        plugins: {
          reports: '',
          demo: ''
        },
        ignorePlugins: ['demo']
      },
      optionalDependencies: {
        '@ekuaibao/plugin-web-demo': '1.6.14-release.3'
      }
    })
  ).toEqual([])
})
