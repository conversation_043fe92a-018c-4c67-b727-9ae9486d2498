/***************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 2020/7/7 11:33. *
 ***************************************************/

const {
  getPluginFullName,
  getPluginShortName,
  getCom<PERSON>,
  getPatchFiles,
  getEntries
} = require('../../sdks_for_entry/helpers')

it('should fix plugin to full name', () => {
  expect(getPluginFullName('@ekuaibao', 'web', 'xxx')).toBe('@ekuaibao/plugin-web-xxx')
  expect(getPluginFullName('@ekuaibao', 'web', 'plugin-web-xxx')).toBe('@ekuaibao/plugin-web-xxx')
  expect(getPluginFullName('@ekuaibao', 'web', '@ekuaibao/plugin-web-xxx')).toBe('@ekuaibao/plugin-web-xxx')
})

it('should fix plugin to short name', () => {
  expect(getPluginShortName('xxx')).toBe('xxx')
  expect(getPluginShortName('plugin-web-xxx')).toBe('plugin-web-xxx')
  expect(getPluginShortName('@ekuaibao/plugin-web-xxx')).toBe('plugin-web-xxx')
})

it('should get commons', () => {
  const sources = { '@ekuaibao/plugin-web-demo': '', '@ekuaibao/plugin-web-kkk': '' }
  const depts = {
    entryPlugins: {
      demo: ['A', 'B'],
      app: ['D', 'E']
    },
    ignorePlugins: ['K', 'L']
  }

  const options = {
    namespace: '@ekuaibao',
    entryName: 'web',
    depts,
    sources
  }
  expect(getCommons(options)).toEqual(['@ekuaibao/plugin-web-demo', '@ekuaibao/plugin-web-kkk'])

  sources['@ekuaibao/plugin-web-A'] = ''
  expect(getCommons(options)).toEqual(['@ekuaibao/plugin-web-demo', '@ekuaibao/plugin-web-kkk'])

  sources['@ekuaibao/plugin-web-L'] = ''
  expect(getCommons(options)).toEqual(['@ekuaibao/plugin-web-demo', '@ekuaibao/plugin-web-kkk'])
})

it('should get patch files object', () => {
  const sources = { '@ekuaibao/plugin-web-demo': '', '@ekuaibao/plugin-web-kkk': '' }

  expect(getPatchFiles(sources)).toEqual({
    ['@ekuaibao/plugin-web-demo']: {
      path: 'node_modules/@ekuaibao/plugin-web-demo',
      to: 'assets/plugin-web-demo'
    },
    ['@ekuaibao/plugin-web-kkk']: {
      path: 'node_modules/@ekuaibao/plugin-web-kkk',
      to: 'assets/plugin-web-kkk'
    }
  })
})

it('should get obj for entries', () => {
  const sources = { '@ekuaibao/plugin-web-demo': '', '@ekuaibao/plugin-web-kkk': '' }
  const depts = {
    entryPlugins: {
      demo: ['demo', 'B'],
      kkk: ['kkk', 'E']
    }
  }

  const options = {
    namespace: '@ekuaibao',
    entryName: 'web',
    depts,
    sources
  }

  expect(getEntries(options)).toEqual({ demo: ['@ekuaibao/plugin-web-demo'], kkk: ['@ekuaibao/plugin-web-kkk'] })

  sources['@ekuaibao/plugin-web-B'] = ''
  expect(getEntries(options)).toEqual({
    demo: ['@ekuaibao/plugin-web-demo', '@ekuaibao/plugin-web-B'],
    kkk: ['@ekuaibao/plugin-web-kkk']
  })

  sources['@ekuaibao/plugin-web-E'] = ''
  expect(getEntries(options)).toEqual({
    demo: ['@ekuaibao/plugin-web-demo', '@ekuaibao/plugin-web-B'],
    kkk: ['@ekuaibao/plugin-web-kkk', '@ekuaibao/plugin-web-E']
  })

  sources['@ekuaibao/plugin-web-OOO'] = ''
  expect(getEntries(options)).toEqual({
    demo: ['@ekuaibao/plugin-web-demo', '@ekuaibao/plugin-web-B'],
    kkk: ['@ekuaibao/plugin-web-kkk', '@ekuaibao/plugin-web-E']
  })
})
