/***************************************************
 * Created by nany<PERSON><PERSON>feng on 2019/9/17 17:56. *
 ***************************************************/

it('should support patch.htmls.1', () => {
  const config = require('../configs')()
  config.patch.htmls({
    ['*']: {
      a: '0000.hbs',
      i: '0000.icon'
    }
  })

  config.patch.htmls({
    ['*']: {
      a: '1111.hbs'
    }
  })

  expect(config.$$cache).toMatchSnapshot()
  expect(config.toConfig()).toMatchSnapshot()
})

it('should support patch.htmls.2', () => {
  const config = require('../configs')()
  config.patch.htmls({
    ['*']: {
      a: '0000.hbs',
      i: '0000.icon'
    }
  })

  config.patch.htmls({
    ['*']: {
      a: '1111.hbs'
    }
  })

  config.patch.htmls({
    ['*']: {
      i: '1111.icon'
    }
  })

  expect(config.$$cache).toMatchSnapshot()
  expect(config.toConfig()).toMatchSnapshot()
})
