const path = require('path')
const config = require('./configs')()

config.output
  .path(path.resolve(process.cwd(), 'build'))
  .filename('[name].[hash].js')
  .publicPath(process.env.ASSET_PATH || './')

const { entryName } = config.getEntryNameAndPluginName()

config.useClusterMaster({
  masterId: entryName,
  injected: `
    var __WHISPERED_PLUGINS__ = window.__WHISPERED_PLUGINS__ = window.__WHISPERED_PLUGINS__ || {};
    try{require('./.dist/assets/entry-${entryName}')}catch(e){}
  `
})

require('./sdks_for_entry/for-entry-patchs')(config)

module.exports = config
