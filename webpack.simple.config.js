const path = require('path')
const config = require('./configs')()
const env = process.env.NODE_ENV || 'development'
const isProduction = env === 'production'

config.optimization.merge({
  runtimeChunk: false,
  splitChunks: {
    chunks: 'all',
    name: false
  }
})

config.output
  .path(path.resolve(process.cwd(), 'dist'))
  .filename(isProduction ? '[name]-[contenthash].js' : '[name]-[hash].js')
  .chunkFilename('[name].chunk-[contenthash].js')
  .pathinfo(true)
  .publicPath(process.env.ASSET_PATH || './')

module.exports = config
