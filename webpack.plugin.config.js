/***************************************************
 * Update by wangguanjia on 2022-09-22
 ***************************************************/
const path = require('path')
const fs = require('fs')
const config = require('./configs')()

const { entryName, pluginName, moduleName, UniqueName } = config.getEntryNameAndPluginName()

const __has = (name) => fs.existsSync(path.join(process.cwd(), name))

const __index = () => {
  if (__has('./src/index.ts')) return './src/index.ts'
  if (__has('./src/index.tsx')) return './src/index.tsx'
  if (__has('./src/index.js')) return './src/index.js'
  if (__has('./src/index.jsx')) return './src/index.jsx'
  throw new Error('没有找到 ./src/index.[jt]sx?')
}

config.entry(moduleName).add(__index())

config.output
  .path(path.resolve(process.cwd(), 'build'))
  .filename('[name].[contenthash].js')
  .chunkFilename('[name].[contenthash].js')
  .libraryTarget('assign')
  .library(['__WHISPERED_PLUGINS__', pluginName])
  .jsonpFunction(`jsonp__${UniqueName}`)
  .hotUpdateFunction(`hot__${UniqueName}`)
  .libraryExport('default')
  // this is way Ⅱ, send template parameters through webpack.output
  .publicPath(`/assets/${moduleName}/`)

// add package.json file
config.plugin('PackageJSONPlugin').use(require('./shells/PackageJSONPlugin'), [{ entry: moduleName }])

config.useClusterWorker({ masterId: entryName, workerId: moduleName, entry: moduleName })

module.exports = config
