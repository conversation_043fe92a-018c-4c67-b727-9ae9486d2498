{"name": "whispered-build", "version": "3.3.12", "bin": {"pull-plugins": "./scripts/pull-plugins.sh.js", "push-plugin": "./scripts/push-plugin.sh.js", "scripts-build": "./shells/build", "scripts-dev": "./shells/dev", "scripts-builds-pro": "./shells/builds-pro", "scripts-builds-dev": "./shells/builds-dev"}, "resolutions": {"fsevents": "", "babel-core": "^7.0.0-bridge.0"}, "dependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-async-generator-functions": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-function-bind": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.0", "@babel/plugin-proposal-throw-expressions": "^7.12.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-default-from": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-function-bind": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.9.0", "@babel/plugin-transform-object-assign": "^7.8.3", "@babel/plugin-transform-regenerator": "^7.8.7", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.0", "@babel/preset-react": "^7.9.4", "@babel/runtime": "^7.9.2", "@babel/runtime-corejs3": "^7.9.2", "@svgr/webpack": "^5.3.0", "autoprefixer": "^9.7.5", "babel-core": "^7.0.0-bridge.0", "babel-loader": "^8.1.0", "babel-plugin-import": "^1.13.0", "case-sensitive-paths-webpack-plugin": "^2.3.0", "children-dirs": "^2.0.0", "copy-webpack-plugin": "^5.1.1", "core-js": "^3", "css-loader": "^3.4.2", "deepmerge": "^4.2.2", "expose-loader": "^0.7.5", "file-loader": "^6.0.0", "fs-extra": "^9.0.0", "glob": "^7.1.6", "html-webpack-plugin": "^4.0.3", "html-webpack-tags-plugin": "^2.0.17", "ip": "^1.1.5", "json5-loader": "^3.0.0", "less": "^3.11.1", "less-loader": "^5.0.0", "less-variable-2-css-variable-loader": "^1.0.0", "mini-css-extract-plugin": "^0.9.0", "mustache-loader": "^1.4.3", "optimize-css-assets-webpack-plugin": "^5.0.3", "parallel-webpack": "^2.4.0", "portfinder": "^1.0.25", "postcss": "^7.0.27", "postcss-flexbugs-fixes": "^4.2.0", "postcss-loader": "^3.0.0", "postcss-pxtorem": "^5.1.1", "postcss-px-multiple": "^0.1.5", "regenerator-runtime": "^0.13.5", "replacer-webpack-plugin": "^1.0.0", "rimraf": "^3.0.2", "simple-git": "^1.132.0", "source-map-loader": "^0.2.4", "style-loader": "^1.1.3", "terser-webpack-plugin": "^2.3.5", "ts-import-plugin": "^1.6.5", "ts-loader": "^6.2.2", "tslib": "2.0.3", "typescript": "4.1.2", "url-loader": "^4.0.0", "webpack": "^4.42.1", "webpack-bundle-analyzer": "^3.6.1", "webpack-chain": "^6.4.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3", "webpack-log": "*", "webpack-micro-cluster": "^1.1.1", "workerize-loader": "^1.1.0", "svgo-loader": "^2.2.1", "svgo": "^1.3.2"}, "devDependencies": {"@types/fs-extra": "*", "jest": "^25", "prettier": "^2.2.1", "react": "^16", "react-dom": "^16"}, "peerDependencies": {"react": "^16", "react-dom": "^16"}, "publishConfig": {"registry": "https://npm.ekuaibao.com/"}, "repository": {"type": "git", "url": "********************:team-shimo/whispered-build.git"}, "jest": {"testEnvironment": "node", "testMatch": ["**/__tests__/**/*.spec.js"], "testPathIgnorePatterns": ["/node_modules/", "/fixtures/"], "coveragePathIgnorePatterns": ["/node_modules/", "/__tests__/", "/dist/"]}, "prettier": {"semi": false, "singleQuote": true, "printWidth": 120, "trailingComma": "none", "tabWidth": 2}, "typings": "index.d.ts"}