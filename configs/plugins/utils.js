/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/3/1 下午3:15
 */
const path = require('path')
const fs = require('fs')
const SCRIPT_EXT = ['.js', '.jsx', '.ts', '.tsx']
const { suffixTag } = require('../const.env')
function resolveFilePath(p, extArrs = SCRIPT_EXT) {
  const realPath = p
  const tagEnv = suffixTag
  for (let i = 0; i < extArrs.length; i++) {
    const item = extArrs[i]
    if (tagEnv) {
      if (fs.existsSync(`${p}.${tagEnv}${item}`)) { // index.js
        return `${p}.${tagEnv}${item}`
      }
      if (fs.existsSync(`${p}${path.sep}index.${tagEnv}${item}`)) { // bb/index.smg.js
        return `${p}${path.sep}index.${tagEnv}${item}`
      }
      if (fs.existsSync(`${p.replace(/\/index$/, `.${tagEnv}/index`)}${item}`)) {//.smg/index.js
        return `${p.replace(/\/index$/, `.${tagEnv}/index`)}${item}`
      }
    }
    if (fs.existsSync(`${p}${item}`)) { //aa.js
      return `${p}${item}`
    }
    if (fs.existsSync(`${p}${path.sep}index${item}`)) { // cc/index.js
      return `${p}${path.sep}index${item}`
    }
  }
  return realPath
}
module.exports = resolveFilePath
