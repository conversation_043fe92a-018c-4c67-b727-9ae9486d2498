//  change from https://github.com/torrac12/postcss-px-multiple-plugin/blob/master/lib/index.js

var postcss = require('postcss');
var multiple = require('./multiple')

module.exports = postcss.plugin('postcss-multiple', function (options) {
  return function (css, result) {
    var oldCssText = css.toString();
    var exclude = options.exclude;
    if (Array.isArray(exclude)) {
      for (var i = 0; i < exclude.length; i++) {
        const itemRegExp = exclude[i];
        if (
          result &&
          result.opts &&
          (itemRegExp instanceof RegExp) &&
          itemRegExp.test(result.opts.from)
        ) {
          return;
        }
      }      
    }
    var instance = new multiple(options);
    var newCssText = instance.generate(oldCssText);
    var newCssObj = postcss.parse(newCssText);
    result.root = newCssObj;
  };
});
