/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/3/1 下午3:15
 */
const resolveFilePath = require('./utils')
const path = require('path')
class MultiPlatformPlugin {
  constructor(source, target) {
    this.source = source // 当前插件挂在哪个钩子下
    this.target = target // 触发的下一个钩子
  }

  apply(resolver) {
    const target = resolver.ensureHook(this.target)
    resolver.getHook(this.source).tapAsync('MultiPlatformPlugin', (request, resolveContext, callback) => {
      const innerRequest = request.request || request.path
      if (!innerRequest || !request.context.issuer) return callback()
      if (!path.extname(innerRequest)) {
        let srcRequest
        if (path.isAbsolute(innerRequest)) {
          // absolute path
          srcRequest = innerRequest
        } else if (!path.isAbsolute(innerRequest) && /^\./.test(innerRequest)) {
          // relative path
          srcRequest = path.resolve(request.path, request.request)
        } else {
          return callback()
        }

        if (/node_modules/.test(srcRequest)) {
          return callback()
        }
        const newRequestStr = resolveFilePath(srcRequest)
        if (newRequestStr === innerRequest) return callback()
        const obj = Object.assign({}, request, {
          request: newRequestStr
        })
        return resolver.doResolve(target, obj, 'resolve multi platform file path', resolveContext, (err, result) => {
          if (err) return callback(err)

          if (result === undefined) return callback(null, null)
          return callback(null, result)
        })
      }

      callback()
    })
  }
}

module.exports = MultiPlatformPlugin
