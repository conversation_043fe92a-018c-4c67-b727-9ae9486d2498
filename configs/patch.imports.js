/***************************************************
 * Created by nanyuanting<PERSON> on 2019/9/17 17:47. *
 ***************************************************/
module.exports = function(options) {
  return () => {
    if (options && options.length) {
      require('./const.options.babel').setImports(options)
      require('./const.options.ts').setImports(options)
    }
  }
}

// 添加新的方法用于设置 Babel 插件
module.exports.plugins = function(plugins) {
  return () => {
    if (plugins && plugins.length) {
      require('./const.options.babel').setPlugins(plugins)
    }
  }
}
