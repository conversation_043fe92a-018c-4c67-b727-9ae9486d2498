/***************************************************
 * Created by nanyuantingfeng on 2019-06-25 17:27. *
 ***************************************************/
const pxtorem = require('postcss-pxtorem')
const autoprefixer = require('autoprefixer')
const flexbugsfixes = require('postcss-flexbugs-fixes')
const MiniCSSExtractPlugin = require('mini-css-extract-plugin')
const { isDevelopment } = require('./const.env')
const PostCssPxMultiple = require('./plugins/postcss-px-multiple')

module.exports = function (config) {
  const useStyleRule = (config) => (lang, test, loaders) => {
    const baseRule = config.module.rule(lang).test(test)
    const modulesRule = baseRule.oneOf('modules').resource(/module\.\w+ss$/)
    const normalRule = baseRule.oneOf('normal')

    applyRules(modulesRule, true)
    applyRules(normalRule, false)

    function applyRules(rule, isModules) {
      if (!!config.$$cache.__use_mini_css__ && !isDevelopment) {
        rule.use('mini-css-extract').loader(MiniCSSExtractPlugin.loader)
      } else {
        rule.use('style-loader').loader('style-loader')
      }

      rule
        .use('css-loader')
        .loader('css-loader')
        .options({
          sourceMap: isDevelopment,
          modules: isModules && {
            localIdentName: '[local]--[hash:base64:8]'
          }
        })

      rule
        .use('postcss-loader')
        .loader('postcss-loader')
        .options({
          plugins: [
            flexbugsfixes,

            autoprefixer({
              overrideBrowserslist: config.$$cache.browserslist,
              flexbox: 'no-2009'
            }),

            config.$$cache.pxtorem ? pxtorem(config.$$cache.pxtorem) : undefined,
            config.$$cache.pxMultiple ? PostCssPxMultiple({ times: 1/2, ...config.$$cache.pxMultiple }) : undefined,
          ].filter(Boolean),

          sourceMap: isDevelopment
        })

      if (loaders && loaders.length) {
        loaders.forEach((loaderDef) => {
          rule.use(loaderDef[0]).loader(loaderDef[0]).options(loaderDef[1])
        })
      }
    }
  }

  useStyleRule(config)('css', /\.css$/)

  const { lessVariable2CssVariableOptions, ...lessOptions } = config.$$cache.less || {}
  useStyleRule(config)(
    'less',
    /\.less$/,
    [
      [
        'less-loader',
        {
          javascriptEnabled: true,
          sourceMap: true,
          modifyVars: {},
          ...lessOptions
        }
      ],
      lessVariable2CssVariableOptions && ['less-variable-2-css-variable-loader', lessVariable2CssVariableOptions]
    ].filter(Boolean)
  )

  if (!!config.$$cache.__use_mini_css__ && !isDevelopment) {
    config.plugin('MiniCSSExtractPlugin').use(MiniCSSExtractPlugin, [
      {
        filename: '[name]-[contenthash].css',
        chunkFilename: '[name]-[contenthash].chunk.css',
        ...(config.$$cache.__use_mini_css__options__ || {})
      }
    ])
  }
}
