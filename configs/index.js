/***************************************************
 * Created by nanyuantingfeng on 2019-06-25 17:31. *
 ***************************************************/
const deepmerge = require('deepmerge')
const Config = require('webpack-chain')

/**
 * @returns {Config}
 */
module.exports = function () {
  const config = new Config()

  require('./config.base')(config)
  require('./config.dev.server')(config)
  require('./config.plugins')(config)
  require('./config.optimization')(config)
  require('./config.devtool')(config)
  require('./config.analyzer')(config)

  config.getPackageObject = () => {
    const { packageMap } = require('./const.env')
    return packageMap
  }

  config.getEntryNameAndPluginName = () => {
    const packageMap = config.getPackageObject()
    // plugin package.json 中的 name 一定是 @namespace/plugin-<entry>-<name> 的形式
    const __package_names = packageMap.name.split('/')
    const namespace = __package_names.shift() // @namespace

    if (!namespace.startsWith('@')) {
      console.error(`invalid package name: ${packageMap.name}, should be @namespace/entry-<name>`)
      process.exit(1)
    }

    const moduleName = __package_names.pop() // plugin-<entry>-<name>
    const UniqueName = moduleName.replace(/-/gim, '_') // plugin_<entry>_<name>

    if (moduleName.startsWith('entry')) {
      const result = moduleName.match(/entry-(.*)$/)

      if (!result) {
        console.error(`invalid module name: ${moduleName}, should be @namespace/entry-<name>`)
        process.exit(1)
      }

      const [, entryName] = result
      return { namespace, entryName, moduleName, UniqueName }
    }

    if (moduleName.startsWith('plugin')) {
      const result = moduleName.match(/plugin-([^-]*)-(.*)$/)

      if (!result) {
        console.error(`invalid module name: ${moduleName}, should be @namespace/plugin-<entry>-<name>`)
        process.exit(1)
      }

      const [, entryName, pluginName] = result
      return { namespace, entryName, pluginName, moduleName, UniqueName }
    }

    console.error(
      `invalid module name: ${moduleName}, should be @namespace/entry-<name> or @namespace/plugin-<entry>-<name>`
    )
    process.exit(1)
  }

  config.useClusterWorker = (options) => {
    require('./patch.development').forPlugin(options)(config)
    return config
  }
  config.useClusterMaster = (options) => {
    require('./patch.development').forEntry(options)(config)
    return config
  }

  const ___ES5_BROWSERS_LIST = [
    'last 2 versions',
    'Firefox >= 33',
    '> 1%',
    'IE >= 11',
    'iOS >= 8',
    'Android >= 4',
    'chrome >= 39',
    'Edge >= 12',
    'Safari >= 9'
  ]

  const ___ES6_BROWSERS_LIST = [
    'last 2 Chrome versions',
    'not Chrome < 60',
    'last 2 Safari versions',
    'not Safari < 10.1',
    'last 2 iOS versions',
    'not iOS < 10.3',
    'last 2 Firefox versions',
    'not Firefox < 54',
    'last 2 Edge versions',
    'not Edge < 15'
  ]

  config.$$cache = {
    browserslist: ___ES5_BROWSERS_LIST,
    __use_mini_css__: true,
    compilertarget: 'es5',
    imageOptions: {  // 添加图片处理配置
      base64Limit: 10240,   // base64 转换阈值，默认 10KB
      svgoEnable: false,     // 是否启用 SVG 优化
      svgoPlugins: [],
    }
  }

  config.patch = (options) => {
    config.$$cache = deepmerge(config.$$cache, options)
  }
  config.patch.defines = (defines) => {
    if (defines) config.$$cache.defines = deepmerge(config.$$cache.defines, defines)
  }
  config.patch.provides = (provides) => {
    if (provides) config.$$cache.provides = deepmerge(config.$$cache.provides, provides)
  }
  config.patch.externals = (externals) => {
    if (externals) config.$$cache.externals = deepmerge(config.$$cache.externals, externals)
  }
  config.patch.noParse = (noParse) => {
    if (noParse) config.$$cache.noParse = deepmerge(config.$$cache.noParse || [], noParse)
  }
  config.patch.htmls = (htmls) => {
    if (htmls) config.$$cache.htmls = deepmerge(config.$$cache.htmls, htmls)
  }
  config.patch.vendors = (vendors) => {
    if (vendors) config.$$cache.vendors = deepmerge(config.$$cache.vendors || [], vendors)
  }
  config.patch.files = (files) => {
    if (files) config.$$cache.files = deepmerge(config.$$cache.files, files)
  }
  config.patch.sdks = (sdks) => {
    if (sdks) config.$$cache.sdks = deepmerge(sdks, config.$$cache.sdks || {})
  }
  config.patch.entry = (entry) => {
    if (entry) {
      if (typeof entry === 'string') {
        entry = { index: entry }
      }
      config.$$cache.entry = deepmerge(config.$$cache.entry, entry)
    }
  }
  config.patch.imports = (imports) => {
    if (imports) {
      if (!Array.isArray(imports)) {
        imports = [imports]
      }
      config.$$cache.imports = (config.$$cache.imports || []).concat(imports)
    }
  }

  // 添加 babelPlugins 配置方法
  config.patch.babelPlugins = (plugins) => {
    if (plugins) {
      if (!Array.isArray(plugins)) {
        plugins = [plugins]
      }
      config.$$cache.babelPlugins = (config.$$cache.babelPlugins || []).concat(plugins)
    }
  }

  config.patch.browserslist = (browserslist, compilertarget) => {
    compilertarget = compilertarget || config.$$cache.compilertarget

    if (browserslist === 'es6') {
      browserslist = ___ES6_BROWSERS_LIST
      compilertarget = 'es6'
    }

    if (browserslist === 'es5') {
      browserslist = ___ES5_BROWSERS_LIST
      compilertarget = 'es5'
    }

    if (!Array.isArray(browserslist)) {
      throw new Error("config.patch.browserslist() argument must one of  'es6' | 'es5' | `string[]` ")
    }

    config.$$cache.browserslist = browserslist
    config.$$cache.compilertarget = compilertarget
  }
  config.patch.pxtorem = (options) => {
    if (options === true) {
      config.$$cache.pxtorem = { rootValue: 100 }
      return
    }
    config.$$cache.pxtorem = options
  }
  config.patch.pxMultiple = (options) => {
    if (options === true) {
      config.$$cache.pxMultiple = { times: 1/2 }
      return
    }
    config.$$cache.pxMultiple = options
  }
  config.patch.alias = (options) => {
    if (options) config.resolve.alias.merge(options)
  }

  config.patch.less = (options) => {
    if (options) config.$$cache.less = options
  }

  config.useExtractCSS = (status, options) => {
    config.$$cache.__use_mini_css__ = status
    if(options) config.$$cache.__use_mini_css__options__ = options
    return config
  }

  // 添加图片配置方法
  config.patch.imageOptions = (options) => {
    if (options) {
      config.$$cache.imageOptions = {
        ...config.$$cache.imageOptions,
        ...options
      }
    }
  }

  // OLD toConfig at Lazy Call
  const toConfig = config.toConfig.bind(config)

  // https://github.com/TypeStrong/ts-loader/issues/751
  // transpileOnly is enabled, export interface not being found!!!!
  config.stats({ warningsFilter: /export .* was not found in/ })

  config.toConfig = () => {
    require('./patch.standalone')(config)
    require('./patch')(config.$$cache)(config)
    require('./config.resources')(config)
    require('./config.css')(config)
    require('./config.script')(config)
    return toConfig()
  }
  return config
}
