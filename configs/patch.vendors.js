/***************************************************
 * Created by nany<PERSON>ing<PERSON> on 2019-08-13 12:12. *
 ***************************************************/
const glob = require('glob')
const path = require('path')
const fs = require('fs')
const deepmerge = require('deepmerge')

const resolve = (moduleName, fileName) =>
  require.resolve(`${moduleName}${path.sep}${fileName}`, { paths: [process.cwd()] })

const getFileName = (regx, cwd) => {
  const files = glob.sync(regx, { cwd })

  if (!Array.isArray(files) || files.length === 0) {
    process.stdout.write(`没有找到文件: ${regx}`)
  }

  if (files.length > 1) {
    process.stdout.write(`匹配到多个文件: ${regx} | ${files.join(' ')}`)
  }

  return files[0]
}

const getPathName = moduleName => {
  const aP = resolve(moduleName, 'package.json')
  const aPn = path.dirname(aP)
  return path.relative(process.cwd(), aPn)
}

const getVendors = regx => moduleNames =>
  moduleNames.map(moduleName => {
    const pathDir = getPathName(moduleName)
    const fileName = getFileName(regx, pathDir)
    return { pathDir, fileName }
  })

const getExternals = moduleNames => {
  let oo = {}
  moduleNames.forEach(moduleName => {
    // alias.js文件内是一个extenals对象，这样的配置只能应用于已单独处理好（添加alias.js文件）的模块
    // 如果不存在alias.js文件，会导致编译失败，try catch是为了避免这种情况，兼容自定义externals配置
    try {
      const alias = resolve(moduleName, 'alias.js')
      if (fs.existsSync(alias)) {
        oo = { ...oo, ...require(alias) }
      }
    } catch (e) {
      // ignore
    }
  })
  return oo
}

const parseOne = regx => moduleNames => {
  const files = {}
  const sdks = []
  const externals = {}

  if (!regx || !moduleNames || !moduleNames.length) {
    return [files, sdks, externals]
  }

  const vendors = getVendors(regx)(moduleNames)

  vendors.forEach(({ fileName, pathDir }) => {
    files[fileName] = pathDir + path.sep + fileName
    sdks.push(fileName)
  })

  Object.assign(externals, getExternals(moduleNames))

  return [files, sdks, externals]
}

module.exports = vendors => {
  vendors = vendors || []

  let files = {}
  let sdks = []
  let externals = {}

  for (let j = 0; j < vendors.length - 1; j += 2) {
    const nn = parseOne(vendors[j])(vendors[j + 1])
    files = deepmerge(files, nn[0])
    sdks = deepmerge(sdks, nn[1])
    externals = deepmerge(externals, nn[2])
  }

  return [files, sdks, externals]
}
