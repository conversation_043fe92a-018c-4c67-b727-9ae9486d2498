/***************************************************
 * Created by nany<PERSON>ingfeng on 2019-06-25 17:29. *
 ***************************************************/

module.exports = function (config) {
  // 获取图片处理配置
  const imageOptions = config.$$cache.imageOptions || {}
  const limit = imageOptions.base64Limit || 1024 * 10
  // 获取不需要内联的图片目录列表
  const excludeBase64Dirs = imageOptions.excludeBase64Dirs || []
  const excludeBase64Pattern = excludeBase64Dirs.length > 0 ? new RegExp(excludeBase64Dirs.join('|')) : null

  // 设置字体文件处理规则
  const fontOptions = { limit, esModule: false }
  config.module
    .rule('svgx')
    .test(/\.svgx$/)
    .use('svgx')
    .loader('@svgr/webpack')
    .end()
    .end()

  config.module
    .rule('json5')
    .test(/\.json5$/)
    .use('json5')
    .loader('json5-loader')
    .end()
    .end()

  config.module
    .rule('woff')
    .test(/\.(woff|woff2)?(\?v=\d+\.\d+\.\d+)?$/)
    .use('font')
    .loader('file-loader')
    .options({ ...fontOptions, mimetype: 'application/font-woff' })
    .end()
    .end()

  config.module
    .rule('ttf')
    .test(/\.ttf(\?v=\d+\.\d+\.\d+)?$/)
    .use('font')
    .loader('file-loader')
    .options({ ...fontOptions, mimetype: 'application/octet-stream' })
    .end()
    .end()

  config.module
    .rule('eot')
    .test(/\.eot(\?v=\d+\.\d+\.\d+)?$/)
    .use('font')
    .loader('file-loader')
    .options({ ...fontOptions, mimetype: 'application/vnd.ms-fontobject' })
    .end()
    .end()

  // 处理图片和SVG文件
  const mediaOptions = { 
    esModule: false, 
    name: 'static/media/[name].[hash].[ext]' 
  }

  const getImageRule = (name) => {
    return config.module
      .rule(name)
      .test(/\.(bmp|png|jpe?g|gif)(\?v=\d+\.\d+\.\d+)?$/i)
  }

  const getSvgRule = (name) => {
    return config.module
    .rule(name)
    .test(/\.svg(\?v=\d+\.\d+\.\d+)?$/)
  }
  
  // 如果有需要排除base64处理的目录
  if (excludeBase64Pattern) {
    // 为排除目录的图片配置file-loader
    getImageRule('img-no-base64')
      .include.add(excludeBase64Pattern).end()
      .use('img-file')
      .loader('file-loader')
      .options(mediaOptions)
      .end()
      .end()

    // 为排除目录的SVG配置file-loader
    getSvgRule('svg-no-base64')
      .include.add(excludeBase64Pattern).end()
      .use('svg-file')
      .loader('file-loader')
      .options({ ...mediaOptions, mimetype: 'image/svg+xml' })
      .end()
      .end()
  }

  // 配置普通图片处理规则（可能排除特定目录）
  const imgRule = getImageRule('img')
  if (excludeBase64Pattern) {
    imgRule.exclude.add(excludeBase64Pattern).end()
  }
  imgRule
    .use('img')
    .loader('url-loader')
    .options({ ...mediaOptions, limit })
    .end()

  // 配置普通SVG处理规则（可能排除特定目录）
  const svgRule = getSvgRule('svg')
  if (excludeBase64Pattern) {
    svgRule.exclude.add(excludeBase64Pattern).end()
  }
  svgRule.use('svg')
    .loader('url-loader')
    .options({ ...mediaOptions, limit, mimetype: 'image/svg+xml' })
    .end()

  // 添加可选的 SVG 优化配置
  if (imageOptions.svgoEnable) {
    config.module
      .rule('svgo-loader')
      .test(/\.svg(\?v=\d+\.\d+\.\d+)?$/)
      .use('svgo-loader')
      .loader('svgo-loader')
      .options({
        plugins: [
          {removeTitle: true},
          {removeViewBox: false},
          {removeComments: true},
          {removeMetadata: true},
          ...(imageOptions.svgoPlugins || [])
        ]
      })
      .end()
      .end()
  }

  config.module
    .rule('html')
    .test(/\.html?$/)
    .use('html')
    .loader('file-loader')
    .options({
      esModule: false,
      name: 'static/html/[name].[hash].[ext]'
    })
    .end()
    .end()

  config.module
    .rule('hbs')
    .test(/\.hbs?$/)
    .use('hbs')
    .loader('mustache-loader')
    .options({})
    .end()
    .end()

  // 修改 css 中图片到相对路径
  config.plugin('ReplacerWebpackPlugin').use(require('replacer-webpack-plugin'), [
    {
      includes: /.+\.css$/i,
      search: /url\(.+?(static\/media\/.+?)\)/g,
      replace: 'url(./$1)'
    }
  ])
}