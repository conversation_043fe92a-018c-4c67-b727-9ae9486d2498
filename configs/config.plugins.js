/***************************************************
 * Created by nany<PERSON>ingfeng on 2019-06-25 19:16. *
 ***************************************************/
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin')
// const LodashWebpackPlugin = require('lodash-webpack-plugin')
const { IgnorePlugin } = require('webpack')
const MultiPlatformPlugin=require('./plugins/MultiPlatformPlugin')


module.exports = function(config) {
  config.resolve
      .plugin('MultiPlatformPlugin')
      .use(MultiPlatformPlugin, ['resolve', 'resolve'])
  config.plugin('CaseSensitivePathsPlugin').use(CaseSensitivePathsPlugin)
  config.plugin('IgnorePlugin').use(IgnorePlugin, [/^\.\/locale$/, /moment$/])
  /*  config.plugin('LodashWebpackPlugin').use(LodashWebpackPlugin, [
    {
      shorthands: true,
      paths: true,
      cloning: true,
      flattening: true,
      exotics: true,
      collections: true,
      caching: true
    }
  ])*/
}
