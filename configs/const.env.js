/***************************************************
 * Created by nany<PERSON>ingfeng on 2019-06-25 17:12. *
 ***************************************************/
const path = require('path')
const fs = require('fs')
const log = require('webpack-log')

const env = process.env.NODE_ENV || 'development'
const isProduction = env === 'production'
const isDevelopment = env === 'development'
const isBeta = env === 'beta'
const buildTag = process.env.CI_BUILD_TAG
const suffixTag = getTagName(buildTag)

const cwd = process.cwd()
const root = cwd
const src = path.resolve(root, 'src')
const build = path.resolve(root, 'dist')

const logger = log({ name: 'H<PERSON>LOW', level: 'info' })

global.logger = {
  info(msg) {
    logger.info(colorInfo(msg))
  },
  error(msg) {
    logger.info(colorError(msg))
  }
}

global.logger.info(`NODE_ENV:${env}\n\n`)

const packageMap = getValueByPath(path.join(cwd, 'package.json'))

function getVersion(packageMap) {
  const version = packageMap.version || '0.0.0'
  const versionTail = isBeta ? '-beta' : isDevelopment ? '-dev' : ''
  return [version, `v${version}${versionTail}`]
}

module.exports = {
  isProduction,
  isDevelopment,
  isBeta,
  env,

  root, // 项目根目录
  cwd: root,
  src, // 项目业务代码根目录
  source: src,
  build, // 生成文件目录
  dist: build,
  packageMap,
  buildTag,
  suffixTag,
  getVersion
}

function colorInfo(msg) {
  return `\u001b[1m\u001b[34m${msg}\u001b[39m\u001b[22m`
}

function colorError(msg) {
  return `\u001b[1m\u001b[31m${msg}\u001b[39m\u001b[22m`
}

function getValueByPath(__path) {
  if (!fs.existsSync(__path)) {
    console.error(`${__path} file is not exists!`)
    process.exit(1)
  }

  return require(__path)
}

function getTagName(buildTag) {
  if (!buildTag) return
  const tags = buildTag.split('-')
  if (!tags) return
  return tags.pop()
}
