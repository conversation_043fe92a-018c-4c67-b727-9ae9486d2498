/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/3/1 下午3:21
 */
const fs = require('fs')
const { cwd ,buildTag} = require('./const.env')
const path = require('path')

module.exports = function (config) {
  const tag = buildTag
  if (tag) {
    const tags = tag.split('-')
    let __path = getPath(tags)
    if (!__path) {
      const configName = tags.pop()
      console.warn(`${configName} config file is not exists!`)
      return
    }
    require(__path)(config)
  }
}

function getPath(tags) {
  let flag = tags.pop()
  let fileName = `${flag}.config.js`
  let __path = path.join(`${cwd}/configs`, fileName)
  if (!fs.existsSync(__path)) {
    return null
  }
  return __path
}
