/***************************************************
 * Created by nany<PERSON>ing<PERSON> on 2019-06-26 12:27. *
 ***************************************************/
const config = require('./webpack.plugin.config')

config.output.filename('[name].js').chunkFilename('[name].js')

const { moduleName } = config.getEntryNameAndPluginName()

config.devServer
  .host(require('ip').address())
  .port(require('./scripts/find-free-port')())
  .set('injectClient', false)
  .disableHostCheck(true)
  .hot(false)
  .hotOnly(false)
  .publicPath(`/assets/${moduleName}/`)

module.exports = config
