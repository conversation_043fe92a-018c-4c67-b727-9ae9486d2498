/***************************************************
 * Created by nanyuantingfeng on 2019/12/26 21:07. *
 ***************************************************/
const path = require('path')
const fs = require('fs')

function findIndexFileName(P, name) {
  return fs.readdirSync(P).find((fn) => fn.endsWith('.js') && fn.startsWith(name))
}

function buildRequire(P, name) {
  const p2 = path.join(P, name)

  if (!fs.existsSync(p2)) {
    console.error(`没有找到目录 ${p2} `)
    return ''
  }

  const fileName = findIndexFileName(p2, name)

  if (!fileName) {
    console.error(`没有找到 ${name} 下的入口文件`)
    return ''
  }

  return `require("./${name}/${fileName}");`
}

module.exports = async function concatEntryPlugins(options) {
  const { assets_dist = '.dist/assets', plugins = {}, name } = options

  const P = path.join(process.cwd(), assets_dist)

  if (!fs.existsSync(P)) {
    fs.mkdirSync(P, { recursive: true })
  }

  const content = Object.keys(plugins)
    .map((name) => buildRequire(P, name))
    .join('\n')

  fs.writeFileSync(path.join(P, name + '.js'), content, { flag: 'w' })
}
