{"compilerOptions": {"importHelpers": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "declaration": true, "target": "es5", "module": "esnext", "moduleResolution": "node", "jsx": "react", "esModuleInterop": true, "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "removeComments": true, "strictNullChecks": false, "inlineSourceMap": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "outDir": "dist/lib", "rootDir": "src", "skipLibCheck": true, "lib": ["dom", "es5", "es6", "es7", "es2015.promise", "es2018.promise", "es2015.collection", "es2015.core", "es2015", "es2016", "es2016.array.include", "es2017", "es2017.object", "es2018", "es2015.iterable"]}}