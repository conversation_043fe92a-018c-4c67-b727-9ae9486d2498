/***************************************************
 * Created by nanyuantingfeng on 2020/1/19 10:21. *
 ***************************************************/
const config = require('../../webpack.entry.dev.config')

config.output.publicPath('http://sxxxx.com/xxxxx')
config.patch.entry({ app: './index.js', debugger: './index.js', payment: './index.js' })

config.patch.sdks({
  '*': ['https://aaaaaaaaa.org/a.css', 'http://KKKKK.com/KKK.js'],
  a: ['ddddd.css', 'eeeeee.js'],
  b: ['||ddddd.css', '||eeeeee.js']
})

const { entryName } = config.getEntryNameAndPluginName()

config.useClusterMaster({
  masterId: entryName,
  injected: [
    [`var __WHISPERED_PLUGINS__ = window.__WHISPERED_PLUGINS__ = window.__WHISPERED_PLUGINS__ || {};`],
    [`require('./dist/entry-${entryName}');`, { entryName: name => !['payment', 'shareexpinfo'].includes(name) }],
    [`require("./src/hosting/app/_plugins")`, { entryName: 'app' }],
    [`require("./src/hosting/browser/_plugins")`, { entryName: name => name === 'debugger' }]
  ]
})

module.exports = config.toConfig()
