/***************************************************
 * Created by nanyuanting<PERSON> on 2020/1/19 10:24. *
 ***************************************************/
const webpack = require('webpack')

const compiler = webpack(require('./webpack.config'))

compiler.run((err, stats) => {
  console.log(stats.toString())
  if (err) {
    console.error(err)
    process.on('exit', () => {
      process.exit(1)
    })
  }
})
