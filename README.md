# 一个新的前端编译工具

#### 安装

> npm i -D whispered-build



#### 使用

> 参考 ```__demos__``` 目录





#### 特性

- 实现单体工程的编译/调试支持
- 微服工程的编译/调试支持
- ES6/ES5 编译的版本支持



#### API

|           | API                                                          | Default                                                     | Description                                                  |
| --------- | ------------------------------------------------------------ | :---------------------------------------------------------- | ------------------------------------------------------------ |
| defines   | ```config.patch.defines(data: object)```                     |                                                             | [`webpack.DefinePlugin`函数别名](https://webpack.docschina.org/plugins/define-plugin/#src/components/Sidebar/Sidebar.jsx) |
| provides  | config.patch.provides(data: object)                          |                                                             | [`webpack.ProvidePlugin`函数别名](https://webpack.js.org/plugins/provide-plugin/#root) |
| externals | config.patch.externals(data: object)                         |                                                             | [webpackConfig.externals 的别名](https://webpack.docschina.org/configuration/externals/#src/components/Sidebar/Sidebar.jsx) |
| noParse   | config.patch.noParse(data: RegExp[])                         |                                                             | [阻止 webpack 解析](https://webpack.js.org/configuration/module/#modulenoparse) |
| htmls     | config.patch.htmls(data: object)                             |                                                             | 实现多入口页面的支持, 由`HtmlWebpackPlugin`提供支持          |
| vendors   | config.patch.vendors(data: object)                           |                                                             | 提供一个 vendors 引用接口                                    |
| files     | ```config.patch.files(data :object)```                       | ```object<string, string | { path:string, name?: string}``` | 复制文件/文件夹到 dist 目录                                  |
| sdks      | config.patch.sdks(data: object)                              |                                                             | 为每一个入口提供一个 <script/> 加载 umd 包的方法             |
| imports   | config.patch.imports(data: object)                           |                                                             | babel-plugin-imports 插件的配置参数                          |
| entry     | config.patch.entry(data: object)                             |                                                             | 入口文件的配置集合                                           |
|           | config.patch.browserslist(browsers : "es6 \| es5 \| ... ", target ?: string) |                                                             | [浏览器编译级别的设置参数](https://github.com/browserslist/browserslist) |
| pxtorem   | config.patch.pxtorem(data: boolean or object)                |                                                             | [PostCSS 的插件，可从像素单位生成 rem 单位](https://github.com/cuth/postcss-pxtorem) |
| alias     | config.patch.alias(data: object)                             |                                                             | [resolve.alias](https://webpack.docschina.org/configuration/resolve/#resolve-alias) |
|           |                                                              |                                                             |                                                              |
|           |                                                              |                                                             |                                                              |
|           |                                                              |                                                             |                                                              |
|           |                                                              |                                                             |                                                              |
