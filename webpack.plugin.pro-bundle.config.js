/***************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 2019-06-26 12:27. *
 ***************************************************/
const config = require('./webpack.plugin.config')

config.optimization.merge({
  splitChunks: {
    chunks: 'async',
    minSize: 20000,
    minChunks: 1,
    maxAsyncRequests: 30,
    maxInitialRequests: 30,
    enforceSizeThreshold: 50000,
    cacheGroups: {
      defaultVendors: {
        test: /[\\/]node_modules[\\/]/,
        reuseExistingChunk: true,
        priority: -10,
      },
      default: {
        reuseExistingChunk: true,
        priority: -20,
      },
    },
  },
})

module.exports = config
