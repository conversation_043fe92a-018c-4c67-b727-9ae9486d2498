/***************************************************
 * Created by nany<PERSON>ingfeng on 2020/4/8 15:30. *
 ***************************************************/
const path = require('path')
const fs = require('fs')

const pluginsDir = path.join(process.cwd(), 'src', 'plugins')
const whispered = require(path.join(process.cwd(), 'package.json')).whispered
const hostingDir = path.join(process.cwd(), 'src', 'hosting')
const srcDir = path.join(process.cwd(), 'src')
const dotDistAssetsDir = path.join(process.cwd(), '.dist', 'assets')
const N = require('./helpers').getEntryName()

const {
  buildRequire,
  buildRequireDefault,
  getAllPluginNames,
  getEntryPluginNames,
  getCommonPluginNames,
  exists
} = require('./helpers')

async function createEntryPlugins(whispered) {
  const { plugins, entryPlugins } = whispered
  const entries = Object.keys(entryPlugins)
  const compiledPlugins = Object.keys(plugins)

  entries.forEach((entryName) => {
    const innerPlugins = entryPlugins[entryName]
    const compiledPs = innerPlugins.filter((p) => compiledPlugins.includes(p))
    const uncompiledPs = innerPlugins.filter((p) => !compiledPlugins.includes(p))

    const contentCompiledPs = compiledPs.map((name) => buildRequire(dotDistAssetsDir, `plugin-${N}-${name}`)).join('\n')
    const contentUncompiledPs = uncompiledPs.map((name) => buildRequireDefault('../../plugins', name)).join('\n')

    let content = ''

    if (contentCompiledPs.trim()) {
      content += contentCompiledPs
    }

    if (contentUncompiledPs.trim()) {
      content += `/*****************************************
* 自动生成的文件, 请勿修改
******************************************/
import { app } from '@ekuaibao/whispered'
${contentUncompiledPs}
      `
    }

    const pCC = path.join(hostingDir, entryName, '_plugins.ts')
    fs.writeFileSync(pCC, content)
    console.log(pCC, '已更新')
  })
}

async function basePlugins() {
  let basePluginContent = ''
  const codemodName = 'res-codemod'
  const libName = 'res_lib_sync'
  const codemodExists = await exists(path.join(srcDir,codemodName))
  if (codemodExists) {
    basePluginContent = `${basePluginContent}\n${buildRequireDefault('..',codemodName)}`
  }
  const libExists = await exists(path.join(srcDir, libName))
  if (libExists) {
    basePluginContent = `${basePluginContent}\n${buildRequireDefault('..',libName)}`
  }
  return  basePluginContent
}

async function createCommonPlugins(whispered) {
  const names = await getAllPluginNames(pluginsDir)
  // names.push('report/budget-chart')
  const N0 = getEntryPluginNames(whispered)
  const N1 = getCommonPluginNames(whispered)
  const commonPlugins = names
    .filter((k) => !N0.includes(k))
    .filter((k) => !N1.includes(k))
    .filter((k) => !(whispered['ignorePlugins'] || []).includes(k))

  const content = commonPlugins.map((name) => buildRequireDefault('../plugins', name)).join('\n')
  const basePlugin = await basePlugins()

  const pCC = path.join(hostingDir, '_plugins.ts')
  fs.writeFileSync(
    pCC,
    `/*****************************************
* 自动生成的文件, 请勿修改
******************************************/
import { app } from '@ekuaibao/whispered'
${basePlugin}
${content}  
  `
  )

  console.log(pCC, '已更新')
}

;(async () => {
  await createEntryPlugins(whispered)
  await createCommonPlugins(whispered)
})()
