/***************************************************
 * Created by nany<PERSON>ing<PERSON> on 2020/3/27 13:50. *
 ***************************************************/
const path = require('path')
const createConfig = require('./webpack.plugins.build')
const whispered = require(path.join(process.cwd(), 'package.json')).whispered
const pluginsNeedBuild = Object.keys(whispered.plugins).filter((k) => whispered.plugins[k] === '')
const configs = pluginsNeedBuild.map((name) => createConfig(name))

module.exports = configs
