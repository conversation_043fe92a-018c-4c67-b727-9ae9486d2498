/***************************************************
 * Created by nany<PERSON>ing<PERSON> on 2020/4/1 15:05. *
 ***************************************************/
const run = require('parallel-webpack').run
const configPath = require.resolve('./multi.webpack.config.js')
const createDistEntryIndex = require('./createDistEntryIndex')

run(configPath, { watch: false, stats: true, maxRetries: 0 }, (error) => {
  if (error) {
    console.error(error.message)
    return
  }

  createDistEntryIndex()
})
