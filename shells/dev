#!/usr/bin/env node

const Server = require('webpack-dev-server')
const createLogger = require('webpack-dev-server/lib/utils/createLogger')
const webpack = require('webpack')

function createDomain({ host, port }) {
  return `http://${host}:${port}`
}

function colorInfo(msg) {
  return `\u001b[1m\u001b[34m${msg}\u001b[39m\u001b[22m`
}

function colorError(msg) {
  return `\u001b[1m\u001b[31m${msg}\u001b[39m\u001b[22m`
}

function PromiseDefer() {
  let resolve = void 0
  let reject = void 0
  const promise = new Promise((rs, rj) => {
    resolve = rs
    reject = rj
  })
  return { promise, resolve, reject }
}

process.on('unhandledRejection', err => {
  throw err
})

function startDevServer(webpackConfig) {
  const options = webpackConfig.devServer
  const log = createLogger(options)

  Server.addDevServerEntrypoints(webpackConfig, options)

  let defer = PromiseDefer()

  let compiler

  try {
    compiler = webpack(webpackConfig)
    const ProgressPlugin = webpack.ProgressPlugin
    new ProgressPlugin().apply(compiler)
  } catch (e) {
    throw e
    log.error(colorError(e.message))
    process.exit(1)

    defer.reject(e)
  }

  let server

  try {
    server = new Server(compiler, options, log)
  } catch (e) {
    log.error(colorError(e.message))
    process.exit(1)
    defer.reject(e)
  }

  ;['SIGINT', 'SIGTERM'].forEach(sig => {
    process.on(sig, () => {
      server.close()
      process.exit()
    })
  })

  server.listen(options.port, options.host, err => {
    if (err) throw err
    defer.resolve(server)
    log.info(`\nService is running at ${colorInfo(createDomain(options))}`)
  })

  return defer.promise
}

const createConfig = require('./webpack.plugins.dev')
const name = process.argv[2]
const config = createConfig(name)

startDevServer(config)
