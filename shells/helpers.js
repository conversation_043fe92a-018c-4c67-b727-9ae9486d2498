/***************************************************
 * Created by nany<PERSON>ingfeng on 2020/4/8 15:43. *
 ***************************************************/
const childrenDirs = require('children-dirs')
const path = require('path')
const fs = require('fs')

function findIndexFileName(P, name) {
  return fs.readdirSync(path.join(P, name)).find((fn) => fn.endsWith('.js') && fn.startsWith(name))
}

function buildRequire(P, name) {
  const fileName = findIndexFileName(P, name)
  if (!fileName) {
    throw new Error(`没有找到 ${name} 下的入口文件`)
  }
  return `require("../../../.dist/assets/${name}/${fileName}");`
}

function buildRequireDefault(P, name) {
  return `app.use(require('${P}/${name}').default)`
}

function getEntryPluginNames(whispered) {
  const { entryPlugins } = whispered

  return Object.keys(entryPlugins || {})
    .map((a) => entryPlugins[a])
    .reduce((a, b) => a.concat(b), [])
}

function getCommonPluginNames(whispered = {}) {
  const { plugins = {} } = whispered
  const entryPluginNames = getEntryPluginNames(whispered)
  return Object.keys(plugins).filter((a) => !entryPluginNames.includes(a))
}

async function getAllPluginNames(pluginsDir) {
  let dirs = await childrenDirs(pluginsDir)
  dirs = dirs.map((k) => k.replace(path.resolve(pluginsDir) + path.sep, ''))
  return dirs || []
}

function checkFileExist(pp) {
  console.log(`check file ${pp}`)
  if (!fs.existsSync(path.join(process.cwd(), pp))) {
    throw new Error(`File {cwd}/${pp} is not exists...`)
  }
}

function getEntryName(name) {
  if (!name) name = require(path.join(process.cwd(), 'package.json')).name
  const K = name.split('/').pop()
  return K.split('-').pop()
}

function getNamespace(name) {
  if (!name) name = require(path.join(process.cwd(), 'package.json')).name
  return name.split('/').shift()
}

async function fileExists(path) {
  return new Promise((resolve) => {
    fs.access(path, fs.F_OK, (err) => {
      if (err) {
        return resolve(false)
      }
      return resolve(true)
    })
  })
}

async function exists(path) {
  const jsExists = await fileExists(`${path}.js`)
  const tsExists = await fileExists(`${path}.ts`)
  return jsExists || tsExists
}

module.exports.getEntryPluginNames = getEntryPluginNames
module.exports.getCommonPluginNames = getCommonPluginNames
module.exports.getAllPluginNames = getAllPluginNames
module.exports.findIndexFileName = findIndexFileName
module.exports.buildRequire = buildRequire
module.exports.buildRequireDefault = buildRequireDefault
module.exports.checkFileExist = checkFileExist
module.exports.getEntryName = getEntryName
module.exports.getNamespace = getNamespace
module.exports.exists = exists
