/***************************************************
 * Created by nanyuantingfeng on 2020/3/27 14:56. *
 ***************************************************/
const path = require('path')
const { checkFileExist } = require('./helpers')
const ModuleScopePlugin = require('./ModuleScopePlugin')
const findIndexFile = require('./findIndexFile')

checkFileExist('webpack.config.plugin.dev.js')

module.exports = (pluginName) => {
  const entryName = require('./helpers').getEntryName()
  const moduleName = `plugin-${entryName}-${pluginName}`
  const UniqueName = moduleName.replace(/-/gim, '_')

  const config = require('../configs')()

  const context = path.join(process.cwd(), 'src', 'plugins', pluginName)
  config.context(context)
  config.entry(moduleName).add(findIndexFile(context))
  config.resolve.plugin('ModuleScopePlugin').use(ModuleScopePlugin, [context])

  config.output
    .path(path.join(process.cwd(), '.dist/assets/' + moduleName))
    .filename('[name].js')
    .chunkFilename('[name].js')
    .libraryTarget('assign')
    .library(['__WHISPERED_PLUGINS__', pluginName])
    .jsonpFunction(`jsonp__${UniqueName}`)
    .hotUpdateFunction(`hot__${UniqueName}`)
    .libraryExport('default')
    .publicPath(`/assets/${moduleName}/`)

  config.output.filename('[name].js').chunkFilename('[name].js')

  config.devServer
    .host(require('ip').address())
    .port(require('../scripts/find-free-port')())
    .set('injectClient', false)
    .disableHostCheck(true)
    .set('hot', false)
    .set('hotOnly', false)
    .publicPath(`/assets/${moduleName}/`)

  config.useClusterWorker({ masterId: entryName, workerId: moduleName, entry: moduleName })

  require(path.join(process.cwd(), 'webpack.config.plugin.dev.js'))(config)

  return config.toConfig()
}
