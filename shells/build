#!/usr/bin/env node

const webpack = require('webpack')
const createConfig = require('./webpack.plugins.build')
const name = process.argv[2]
const config = createConfig(name)
const createDistEntryIndex = require('./createDistEntryIndex')

const compiler = webpack(config)

const ProgressPlugin = webpack.ProgressPlugin
new ProgressPlugin().apply(compiler)

compiler.run((error, stats) => {
  if (error) {
    console.error(error.message)
    process.on('exit', () => {
      process.exit(1)
    })
  }

  try {
    const { errors } = stats.toJson()
    if (errors && errors.length) {
      process.on('exit', () => {
        process.exit(1)
      })
    }
  } catch (e) {
    console.error(e.message)
    process.on('exit', () => {
      process.exit(1)
    })
  }

  const buildInfo = stats.toString({ color: true })
  console.info(buildInfo)

  createDistEntryIndex()

  process.on('exit', () => {
    process.exit(1)
  })
})
