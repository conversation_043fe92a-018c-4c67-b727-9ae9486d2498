/***************************************************
 * Created by nanyuanting<PERSON> on 2020/4/9 10:48. *
 ***************************************************/
const path = require('path')
const fs = require('fs')

module.exports = function findIndexFile(pp) {
  const __has = (name) => fs.existsSync(path.join(pp, name))
  if (__has('./index.tsx')) return './index.tsx'
  if (__has('./index.ts')) return './index.ts'
  if (__has('./index.jsx')) return './index.jsx'
  if (__has('./index.js')) return './index.js'
  throw new Error(`没有找到 ./index.[jt]sx?  => ${pp}`)
}
