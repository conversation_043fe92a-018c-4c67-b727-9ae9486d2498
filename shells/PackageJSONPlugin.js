/***************************************************
 * Created by nany<PERSON>ing<PERSON> on 2020/7/2 19:48. *
 ***************************************************/
const path = require('path')
const fs = require('fs')

const PLUGIN_NAME = 'PackageJSONPlugin'

module.exports = class PackageJSONPlugin {
  constructor(options, defaultValue) {
    this.options = Object.assign(
      {
        context: process.cwd(),
        entry: 'main',
        field: 'main',
        fileName: 'package.json'
      },
      options
    )

    this.defaultValue = defaultValue || {}
  }

  apply(compiler) {
    const { entry, context, field, fileName } = this.options
    const __package_path = path.resolve(context, fileName)

    let oo = {}

    if (fs.existsSync(__package_path)) {
      const orgPkg = require(__package_path)
      oo = Object.assign(oo, {
        name: orgPkg.name,
        version: orgPkg.version,
        license: orgPkg.license,
        publishConfig: orgPkg.publishConfig,
        updateTime: new Date()
      })
    }

    oo = Object.assign(oo, this.defaultValue)

    compiler.hooks.emit.tapAsync(PLUGIN_NAME, (compilation, callback) => {
      const stats = compilation.getStats().toJson()

      if (!stats.entrypoints[entry]) {
        throw new Error('No entry was found:' + entry)
      }

      const assets = stats.entrypoints[entry].assets
      oo[field] = assets.find((d) => path.extname(d) === '.js')

      compilation.assets[fileName] = {
        source: () => JSON.stringify(oo, null, 2),
        size: () => Object.keys(oo).length
      }

      callback()
    })
  }
}
