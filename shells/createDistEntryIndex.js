/***************************************************
 * Created by nanyuantingfeng on 2020/4/20 18:28. *
 ***************************************************/
const path = require('path')
const concatEntryPlugins = require('../scripts/concat-entry-plugins')
const packageObject = require(path.join(process.cwd(), 'package.json'))
const { whispered } = packageObject
const { plugins } = whispered
const { getCommonPluginNames } = require('./helpers')
const pluginNames = getCommonPluginNames(whispered)
const pluginNameObject = {}
const N = require('./helpers').getEntryName()
pluginNames.forEach((name) => (pluginNameObject[`plugin-${N}-${name}`] = plugins[name]))

module.exports = function () {
  const name = packageObject.name.split('/').pop()
  concatEntryPlugins({ plugins: pluginNameObject, name: name })
}
