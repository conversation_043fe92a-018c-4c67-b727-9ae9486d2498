/***************************************************
 * Created by nany<PERSON>ingfeng on 2020/7/7 12:33. *
 ***************************************************/
const { getPatchFiles, getPatchSDKs } = require('./helpers')
const checkPluginNames = require('./check-plugins-names')
const chalk = require('chalk')

function isRemoteURL(url) {
  if(!url) return false
  return ['http://', 'file://', 'https://', 'ftp://', '//'].some(k => url.startsWith(k))
}

module.exports = function (config) {
  const { namespace, entryName } = config.getEntryNameAndPluginName()
  const packageObj = config.getPackageObject()
  const __oo = checkPluginNames(packageObj)

  if (__oo && __oo.length) {
    console.error(
      chalk.red(`
    在whispered.plugins 和 optionalDependencies 节点上存在同名的节点,请仔细确认.
    whispered.plugins :: ${chalk.green(__oo.join(','))}
     `)
    )
    process.exit(1)
  }

  const sources = packageObj.optionalDependencies || {}
  const depts = packageObj.whispered || {}

  const options = { namespace, entryName, depts, sources }

  const __ASSET_PATH__ = process.env.ASSET_PATH

  // 如果此处编译版本 `ASSET_PATH` 存在.
  // 说明此版本是sass版本,
  // 资源已经优先托管到CDN上.
  // 就不需要指定目录复制 `files`
  if (isRemoteURL(__ASSET_PATH__)) {
    options.cdn = __ASSET_PATH__
    config.patch.sdks(getPatchSDKs(options))
    return config
  }

  // 此处逻辑没有 `ASSET_PATH` 存在.
  // 说明此版本是dev/standalone版本.
  // 所有资源都存在本地,没有CDN路径配置.
  // 编译过程完全由本地过程制定.
  delete options.cdn
  config.patch.files(getPatchFiles(sources))
  config.patch.sdks(getPatchSDKs(options))
  return config
}
