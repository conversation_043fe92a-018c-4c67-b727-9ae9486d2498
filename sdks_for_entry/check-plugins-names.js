/***************************************************
 * Created by nany<PERSON>ingfeng on 2020/7/14 16:01. *
 ***************************************************/
const { getCommonPluginNames, getEntryName, getNamespace } = require('../shells/helpers')
const { getCommons, getPluginFullName } = require('../sdks_for_entry/helpers')

module.exports = function (obj) {
  const { whispered, optionalDependencies, name } = obj
  const pluginNames = getCommonPluginNames(whispered)

  const __namespace = getNamespace(name)
  const __entryName = getEntryName(name)

  const pluginNamesAtOps = getCommons({
    namespace: __namespace,
    entryName: __entryName,
    depts: whispered,
    sources: optionalDependencies
  })

  return pluginNames.filter((N) => pluginNamesAtOps.includes(getPluginFullName(__namespace, __entryName, N)))
}
