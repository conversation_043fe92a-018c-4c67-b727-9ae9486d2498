/***************************************************
 * Created by nanyuanting<PERSON> on 2020/7/7 11:32. *
 ***************************************************/
const __P = require('path')

/**
 * @param namespace {string} - must be startswith "@"
 * @param entryName {string}
 * @param name {string}
 * @returns {string}
 */
function getPluginFullName(namespace, entryName, name) {
  const __K = namespace + '/plugin-' + entryName
  if (name.startsWith(__K)) {
    return name
  }

  if (name.startsWith('plugin-' + entryName)) {
    return namespace + '/' + name
  }
  return __K + '-' + name
}

/**
 * @param name {string}
 * @returns {string}
 */
function getPluginShortName(name) {
  return name.split('/').pop()
}

/**
 * @param pluginName {string}
 * @param cdn {string | undefined}
 * @returns {string}
 */
function getAssetPath(pluginName, cdn) {
  const __namespace = pluginName.split('/').shift()
  const __a = require.resolve(pluginName)
  const __b = __a.split(__P.join('node_modules', __namespace)).pop()

  if (!cdn) return 'assets' + __b

  if (!cdn.endsWith('/')) {
    return cdn + '/' + 'assets' + __b
  }

  return cdn + 'assets' + __b
}

/**
 * @param arrayA {[]}
 * @param arrayB {[]}
 * @returns {[]}
 */
function different(arrayA, arrayB) {
  const result = []

  for (let i = 0; i < arrayA.length; i++) {
    if (arrayB.indexOf(arrayA[i]) <= -1) {
      result.push(arrayA[i])
    }
  }

  return result
}

/**
 * @param arrayA {[]}
 * @param arrayB {[]}
 * @returns {[]}
 */
function intersection(arrayA, arrayB) {
  return arrayA.filter((v) => arrayB.includes(v))
}

/**
 * @param options {Object}
 * @param options.namespace {string}
 * @param options.entryName {string}
 * @param options.depts {Object}
 * @param options.depts.entryPlugins {Object}
 * @param options.depts.ignorePlugins {[string]}
 * @param options.sources {Object}
 * @returns {[string]}
 */
function getCommons(options) {
  const { namespace, entryName, depts = {}, sources = {} } = options
  // optionalDependencies - entryPlugins - ignorePlugins
  const __entry_plugins = depts.entryPlugins || {}
  const __entry_plugins_array = Object.keys(__entry_plugins).flatMap((k) => __entry_plugins[k])

  const __ignorePlugins_array = depts.ignorePlugins || []
  const __optional_plugins_array = Object.keys(sources)
  const __A = __optional_plugins_array.map((d) => getPluginFullName(namespace, entryName, d))
  const __B = __ignorePlugins_array.concat(__entry_plugins_array).map((d) => getPluginFullName(namespace, entryName, d))

  return different(__A, __B)
}

/**
 * @param options {Object}
 * @param options.namespace {string}
 * @param options.entryName {string}
 * @param options.depts {Object}
 * @param options.depts.entryPlugins {Object}
 * @param options.depts.ignorePlugins {[string]}
 * @param options.sources {Object}
 * @returns {*}
 */
function getEntries(options) {
  const oo = {}
  const __K = Object.keys(options.sources)
  Object.keys(options.depts.entryPlugins || {}).forEach((name) => {
    const __A = options.depts.entryPlugins[name].map((d) => getPluginFullName(options.namespace, options.entryName, d))
    oo[name] = intersection(__K, __A)
  })
  return oo
}

/**
 * for NODE_ENV development or standalone
 * @param sources {Object}
 * @returns {Object}
 */
function getPatchFiles(sources) {
  const oo = {}
  Object.keys(sources).forEach((name) => {
    oo[name] = {
      path: 'node_modules/' + name,
      to: 'assets/' + getPluginShortName(name)
    }
  })
  return oo
}

/**
 * @param options {Object}
 * @param options.namespace {string}
 * @param options.entryName {string}
 * @param options.depts {Object}
 * @param options.depts.entryPlugins {Object}
 * @param options.depts.ignorePlugins {[string]}
 * @param options.sources {Object}
 * @param options.cdn {string=}
 * @returns {Object}
 */
function getPatchSDKs(options) {
  const __cdn = options.cdn
  const oo = {}
  const __commons = getCommons(options)
  oo['*'] = __commons.map((name) => getAssetPath(name, __cdn))

  const __entries = getEntries(options)
  Object.keys(__entries).forEach((name) => {
    oo[name] = __entries[name].map((name) => getAssetPath(name, __cdn))
  })

  return oo
}

module.exports.getPluginFullName = getPluginFullName
module.exports.getPluginShortName = getPluginShortName
module.exports.getAssetPath = getAssetPath
module.exports.getCommons = getCommons
module.exports.getEntries = getEntries
module.exports.getPatchFiles = getPatchFiles
module.exports.getPatchSDKs = getPatchSDKs
